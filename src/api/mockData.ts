import type {
  Member,
  Order,
  DashboardStats,
  Employee,
  CashRegister,
  Transaction,
  Shift,
  Store,
  Product,
  ManagementStats,
  ExchangeRate,
  Role,
  Permission,
  PermissionCategory,
  UserRole,
  FinancialStatement,
  TransactionSummary,
  ReconciliationReport,
  LoginRes,
  LoginProps,
} from '@/types';

// Mock Members Data
export const mockMembers: Member[] = [
  {
    id: '1',
    name: '<PERSON>',
    passport: 'US********9',
    registrationDate: '2024-01-15T10:30:00Z',
    status: 'active',
    email: '<EMAIL>',
    phone: '******-0123',
    dateOfBirth: '1985-03-15',
    nationality: 'United States',
    address: '123 Main St, New York, NY 10001',
    totalOrders: 3,
    totalSpent: 800,
    lastVisit: '2024-02-22T09:15:00Z',
    membershipTier: 'gold',
    notes: 'VIP customer, prefers high-stakes tables',
  },
  {
    id: '2',
    name: '<PERSON>',
    passport: 'FR987654321',
    registrationDate: '2024-01-20T14:45:00Z',
    status: 'active',
    email: '<EMAIL>',
    phone: '+33-1-23-45-67-89',
    dateOfBirth: '1990-07-22',
    nationality: 'France',
    address: '45 Rue de la Paix, 75001 Paris, France',
    totalOrders: 1,
    totalSpent: 750,
    lastVisit: '2024-02-21T14:45:00Z',
    membershipTier: 'silver',
    notes: 'Enjoys slot machines and poker',
  },
  {
    id: '3',
    name: 'Hans Mueller',
    passport: 'DE456789123',
    registrationDate: '2024-02-01T09:15:00Z',
    status: 'inactive',
    email: '<EMAIL>',
    phone: '+49-30-********',
    dateOfBirth: '1978-11-08',
    nationality: 'Germany',
    address: 'Unter den Linden 1, 10117 Berlin, Germany',
    totalOrders: 0,
    totalSpent: 0,
    lastVisit: '2024-02-01T09:15:00Z',
    membershipTier: 'bronze',
    notes: 'Account suspended due to inactivity',
  },
  {
    id: '4',
    name: 'Anna Johnson',
    passport: 'SE789123456',
    registrationDate: '2024-02-10T16:20:00Z',
    status: 'active',
    email: '<EMAIL>',
    phone: '+46-8-123-456-78',
    dateOfBirth: '1992-05-30',
    nationality: 'Sweden',
    address: 'Drottninggatan 10, 111 51 Stockholm, Sweden',
    totalOrders: 1,
    totalSpent: 1000,
    lastVisit: '2024-02-23T16:20:00Z',
    membershipTier: 'platinum',
    notes: 'High roller, frequent visitor',
  },
  {
    id: '5',
    name: 'Carlos Rodriguez',
    passport: 'ES321654987',
    registrationDate: '2024-02-15T11:30:00Z',
    status: 'active',
    email: '<EMAIL>',
    phone: '+34-91-123-45-67',
    dateOfBirth: '1988-12-03',
    nationality: 'Spain',
    address: 'Gran Vía 25, 28013 Madrid, Spain',
    totalOrders: 1,
    totalSpent: 250,
    lastVisit: '2024-02-24T11:30:00Z',
    membershipTier: 'bronze',
    notes: 'New member, interested in blackjack',
  },
  {
    id: '6',
    name: 'Yuki Tanaka',
    passport: 'JP456123789',
    registrationDate: '2024-01-25T08:00:00Z',
    status: 'active',
    email: '<EMAIL>',
    phone: '+81-3-1234-5678',
    dateOfBirth: '1987-09-14',
    nationality: 'Japan',
    address: '1-1-1 Shibuya, Tokyo 150-0002, Japan',
    totalOrders: 5,
    totalSpent: 2500,
    lastVisit: '2024-02-24T20:15:00Z',
    membershipTier: 'platinum',
    notes: 'Regular customer, prefers baccarat and roulette',
  },
  {
    id: '7',
    name: 'Emma Wilson',
    passport: 'AU789456123',
    registrationDate: '2024-02-05T12:30:00Z',
    status: 'inactive',
    email: '<EMAIL>',
    phone: '+61-2-9876-5432',
    dateOfBirth: '1995-04-18',
    nationality: 'Australia',
    address: '100 George St, Sydney NSW 2000, Australia',
    totalOrders: 2,
    totalSpent: 450,
    lastVisit: '2024-02-10T15:45:00Z',
    membershipTier: 'silver',
    notes: 'Temporary suspension pending verification',
  },
];

// Mock Orders Data - Updated to match backend API structure
export const mockOrders: Order[] = [
  {
    orderId: 1,
    orderCode: '462db0a0-95c9-4dcc-8828-54e5055305ae',
    orderType: 'BUY',
    userId: '0be8ffe6-4c46-49fd-a06f-17803514c20c',
    counterId: '0001',
    staffId: '0433ae22-0664-471b-bce4-ed9ab4f3549a',
    quantity: 10,
    pricePerUnit: 50.00,
    currencyUnit: 'USD',
    orderStatus: 'COMPLETED',
    notes: 'High-stakes poker table reservation',
    metadata: null,
    createdAt: '2024-02-20T10:30:00Z',
    updatedAt: '2024-02-20T10:35:00Z',
  },
  {
    orderId: 2,
    orderCode: '7f3e8b2a-1c4d-4e5f-9a8b-2c3d4e5f6a7b',
    orderType: 'SELL',
    userId: '1a2b3c4d-5e6f-7a8b-9c0d-1e2f3a4b5c6d',
    counterId: '0002',
    staffId: '8f7e6d5c-4b3a-2918-7654-3210fedcba98',
    quantity: 5,
    pricePerUnit: 75.50,
    currencyUnit: 'EUR',
    orderStatus: 'PENDING',
    notes: 'Regular customer, preferred slots area',
    metadata: { customerTier: 'gold', preferredArea: 'slots' },
    createdAt: '2024-02-21T14:15:00Z',
    updatedAt: '2024-02-21T14:20:00Z',
  },
  {
    orderId: 3,
    orderCode: 'a1b2c3d4-e5f6-7890-abcd-ef********90',
    orderType: 'BUY',
    userId: '0be8ffe6-4c46-49fd-a06f-17803514c20c',
    counterId: '0001',
    staffId: '0433ae22-0664-471b-bce4-ed9ab4f3549a',
    quantity: 12,
    pricePerUnit: 25.00,
    currencyUnit: 'USD',
    orderStatus: 'COMPLETED',
    notes: 'Regular blackjack session',
    metadata: null,
    createdAt: '2024-02-22T09:15:00Z',
    updatedAt: '2024-02-22T09:20:00Z',
  },
  {
    orderId: 4,
    orderCode: 'f9e8d7c6-b5a4-3210-9876-543210fedcba',
    orderType: 'SELL',
    userId: '2c3d4e5f-6a7b-8c9d-0e1f-2a3b4c5d6e7f',
    counterId: '0003',
    staffId: '8f7e6d5c-4b3a-2918-7654-3210fedcba98',
    quantity: 2,
    pricePerUnit: 500.00,
    currencyUnit: 'USD',
    orderStatus: 'REJECTED',
    notes: 'Payment declined - insufficient funds',
    metadata: { rejectionReason: 'insufficient_funds' },
    createdAt: '2024-02-23T16:20:00Z',
    updatedAt: '2024-02-23T16:25:00Z',
  },
  {
    orderId: 5,
    orderCode: '3e4f5a6b-7c8d-9e0f-1a2b-3c4d5e6f7a8b',
    orderType: 'BUY',
    userId: '4d5e6f7a-8b9c-0d1e-2f3a-4b5c6d7e8f9a',
    counterId: '0004',
    staffId: '0433ae22-0664-471b-bce4-ed9ab4f3549a',
    quantity: 25,
    pricePerUnit: 10.00,
    currencyUnit: 'USD',
    orderStatus: 'PROCESSING',
    notes: 'First-time crypto payment',
    metadata: { paymentMethod: 'crypto' },
    createdAt: '2024-02-24T11:30:00Z',
    updatedAt: '2024-02-24T11:35:00Z',
  },
  {
    orderId: 6,
    orderCode: '9a8b7c6d-5e4f-3210-9876-543210abcdef',
    orderType: 'BUY',
    userId: '5e6f7a8b-9c0d-1e2f-3a4b-5c6d7e8f9a0b',
    counterId: '0001',
    staffId: '0433ae22-0664-471b-bce4-ed9ab4f3549a',
    quantity: 2,
    pricePerUnit: 1000.00,
    currencyUnit: 'JPY',
    orderStatus: 'COMPLETED',
    notes: 'Platinum member - VIP treatment',
    metadata: { customerTier: 'platinum', vipTreatment: true },
    createdAt: '2024-02-24T20:15:00Z',
    updatedAt: '2024-02-24T20:20:00Z',
  },
  {
    orderId: 7,
    orderCode: 'b2c3d4e5-f6a7-8b9c-0d1e-2f3a4b5c6d7e',
    orderType: 'SELL',
    userId: '6f7a8b9c-0d1e-2f3a-4b5c-6d7e8f9a0b1c',
    counterId: '0002',
    staffId: '8f7e6d5c-4b3a-2918-7654-3210fedcba98',
    quantity: 1,
    pricePerUnit: 150.00,
    currencyUnit: 'GBP',
    orderStatus: 'CANCELLED',
    notes: 'Cancelled due to account verification issues',
    metadata: { cancellationReason: 'account_verification_pending' },
    createdAt: '2024-02-10T15:45:00Z',
    updatedAt: '2024-02-10T16:00:00Z',
  },
  {
    orderId: 8,
    orderCode: 'c4d5e6f7-a8b9-0c1d-2e3f-4a5b6c7d8e9f',
    orderType: 'BUY',
    userId: '7a8b9c0d-1e2f-3a4b-5c6d-7e8f9a0b1c2d',
    counterId: '0003',
    staffId: '0433ae22-0664-471b-bce4-ed9ab4f3549a',
    quantity: 8,
    pricePerUnit: 125.75,
    currencyUnit: 'CNY',
    orderStatus: 'CONFIRMED',
    notes: 'Awaiting processing',
    metadata: null,
    createdAt: '2024-02-25T08:30:00Z',
    updatedAt: '2024-02-25T08:35:00Z',
  },
];

// Mock Dashboard Stats
export const mockDashboardStats: DashboardStats = {
  totalMembers: mockMembers.length,
  recentOrders: mockOrders.filter((order) => {
    const orderDate = new Date(order.createdAt);
    const weekAgo = new Date();
    weekAgo.setDate(weekAgo.getDate() - 7);
    return orderDate >= weekAgo;
  }).length,
  pendingTransactions: mockOrders.filter((order) => order.orderStatus === 'PENDING')
    .length,
  totalRevenue: mockOrders
    .filter((order) => order.orderStatus === 'COMPLETED')
    .reduce((sum, order) => sum + (order.quantity * order.pricePerUnit), 0),
};

// Mock Employees Data
export const mockEmployees: Employee[] = [
  {
    id: 'EMP001',
    name: 'Alice Johnson',
    email: '<EMAIL>',
    phone: '******-0101',
    role: 'admin',
    department: 'management',
    status: 'active',
    hireDate: '2023-01-15T00:00:00Z',
    salary: 85000,
    permissions: ['all'],
    lastLogin: '2024-02-24T09:30:00Z',
    performanceScore: 95,
    attendanceRate: 98,
    address: '123 Admin St, Las Vegas, NV 89101',
    emergencyContact: {
      name: 'John Johnson',
      phone: '******-0201',
      relationship: 'Spouse',
    },
    schedule: {
      monday: { start: '08:00', end: '17:00' },
      tuesday: { start: '08:00', end: '17:00' },
      wednesday: { start: '08:00', end: '17:00' },
      thursday: { start: '08:00', end: '17:00' },
      friday: { start: '08:00', end: '17:00' },
      saturday: { off: true, start: '', end: '' },
      sunday: { off: true, start: '', end: '' },
    },
    performanceMetrics: {
      monthlyRating: 95,
      tasksCompleted: 28,
      customerSatisfaction: 96,
      punctuality: 98,
      teamwork: 94,
      lastReviewDate: '2024-01-15T00:00:00Z',
      goals: ['Improve team efficiency', 'Implement new training programs'],
      achievements: [
        'Led successful Q4 project',
        'Reduced operational costs by 15%',
      ],
    },
  },
  {
    id: 'EMP002',
    name: 'Bob Smith',
    email: '<EMAIL>',
    phone: '******-0102',
    role: 'manager',
    department: 'gaming',
    status: 'active',
    hireDate: '2023-03-20T00:00:00Z',
    salary: 65000,
    permissions: ['manage_employees', 'view_reports', 'manage_shifts'],
    lastLogin: '2024-02-24T08:15:00Z',
    performanceScore: 88,
    attendanceRate: 95,
    address: '456 Manager Ave, Las Vegas, NV 89102',
    emergencyContact: {
      name: 'Sarah Smith',
      phone: '******-0202',
      relationship: 'Wife',
    },
    schedule: {
      monday: { start: '14:00', end: '22:00' },
      tuesday: { start: '14:00', end: '22:00' },
      wednesday: { start: '14:00', end: '22:00' },
      thursday: { start: '14:00', end: '22:00' },
      friday: { start: '14:00', end: '22:00' },
      saturday: { start: '12:00', end: '20:00' },
      sunday: { off: true, start: '', end: '' },
    },
    performanceMetrics: {
      monthlyRating: 88,
      tasksCompleted: 25,
      customerSatisfaction: 90,
      punctuality: 92,
      teamwork: 89,
      lastReviewDate: '2024-01-20T00:00:00Z',
      goals: ['Increase gaming floor efficiency', 'Mentor junior staff'],
      achievements: [
        'Improved customer satisfaction by 12%',
        'Reduced wait times',
      ],
    },
  },
  {
    id: 'EMP003',
    name: 'Carol Davis',
    email: '<EMAIL>',
    phone: '******-0103',
    role: 'cashier',
    department: 'customer_service',
    status: 'active',
    hireDate: '2023-06-10T00:00:00Z',
    salary: 35000,
    permissions: ['operate_register', 'process_transactions'],
    lastLogin: '2024-02-23T16:45:00Z',
    performanceScore: 92,
    attendanceRate: 97,
    address: '789 Cashier Blvd, Las Vegas, NV 89103',
    emergencyContact: {
      name: 'Mike Davis',
      phone: '******-0203',
      relationship: 'Brother',
    },
    schedule: {
      monday: { start: '09:00', end: '17:00' },
      tuesday: { start: '09:00', end: '17:00' },
      wednesday: { start: '09:00', end: '17:00' },
      thursday: { start: '09:00', end: '17:00' },
      friday: { start: '09:00', end: '17:00' },
      saturday: { start: '10:00', end: '18:00' },
      sunday: { start: '10:00', end: '18:00' },
    },
    performanceMetrics: {
      monthlyRating: 92,
      tasksCompleted: 30,
      customerSatisfaction: 95,
      punctuality: 96,
      teamwork: 88,
      lastReviewDate: '2024-01-10T00:00:00Z',
      goals: ['Improve transaction speed', 'Learn new POS features'],
      achievements: [
        'Zero cash discrepancies for 3 months',
        'Customer service award',
      ],
    },
  },
  {
    id: 'EMP004',
    name: 'David Wilson',
    email: '<EMAIL>',
    phone: '******-0104',
    role: 'security',
    department: 'security',
    status: 'active',
    hireDate: '2023-08-05T00:00:00Z',
    salary: 45000,
    permissions: ['monitor_premises', 'incident_reports'],
    lastLogin: '2024-02-24T06:00:00Z',
    performanceScore: 90,
    attendanceRate: 99,
    address: '321 Security St, Las Vegas, NV 89104',
    emergencyContact: {
      name: 'Lisa Wilson',
      phone: '******-0204',
      relationship: 'Sister',
    },
    schedule: {
      monday: { start: '22:00', end: '06:00' },
      tuesday: { start: '22:00', end: '06:00' },
      wednesday: { start: '22:00', end: '06:00' },
      thursday: { start: '22:00', end: '06:00' },
      friday: { off: true, start: '', end: '' },
      saturday: { off: true, start: '', end: '' },
      sunday: { start: '22:00', end: '06:00' },
    },
    performanceMetrics: {
      monthlyRating: 90,
      tasksCompleted: 22,
      customerSatisfaction: 88,
      punctuality: 99,
      teamwork: 85,
      lastReviewDate: '2024-02-05T00:00:00Z',
      goals: [
        'Complete advanced security training',
        'Reduce incident response time',
      ],
      achievements: [
        'Perfect attendance record',
        'Prevented 2 security incidents',
      ],
    },
  },
  {
    id: 'EMP005',
    name: 'Eva Martinez',
    email: '<EMAIL>',
    phone: '******-0105',
    role: 'maintenance',
    department: 'maintenance',
    status: 'inactive',
    hireDate: '2023-04-12T00:00:00Z',
    salary: 40000,
    permissions: ['equipment_maintenance', 'facility_repairs'],
    lastLogin: '2024-02-20T14:30:00Z',
    performanceScore: 85,
    attendanceRate: 92,
    address: '654 Maintenance Way, Las Vegas, NV 89105',
    emergencyContact: {
      name: 'Carlos Martinez',
      phone: '******-0205',
      relationship: 'Husband',
    },
    schedule: {
      monday: { start: '06:00', end: '14:00' },
      tuesday: { start: '06:00', end: '14:00' },
      wednesday: { start: '06:00', end: '14:00' },
      thursday: { start: '06:00', end: '14:00' },
      friday: { start: '06:00', end: '14:00' },
      saturday: { off: true, start: '', end: '' },
      sunday: { off: true, start: '', end: '' },
    },
    performanceMetrics: {
      monthlyRating: 85,
      tasksCompleted: 18,
      customerSatisfaction: 82,
      punctuality: 88,
      teamwork: 90,
      lastReviewDate: '2024-01-12T00:00:00Z',
      goals: ['Return to active status', 'Complete safety certification'],
      achievements: [
        'Maintained all equipment for 6 months',
        'Zero safety incidents',
      ],
    },
  },
];

// Mock Cash Registers Data
export const mockCashRegisters: CashRegister[] = [
  {
    id: 'REG001',
    name: 'Main Floor Register 1',
    location: 'Gaming Floor - Section A',
    status: 'open',
    currentBalance: 2500.0,
    openingBalance: 1000.0,
    employeeId: 'EMP003',
    employeeName: 'Carol Davis',
    openedAt: '2024-02-24T08:00:00Z',
    shiftId: 'SHIFT001',
  },
  {
    id: 'REG002',
    name: 'VIP Lounge Register',
    location: 'VIP Lounge',
    status: 'open',
    currentBalance: 5200.0,
    openingBalance: 2000.0,
    employeeId: 'EMP002',
    employeeName: 'Bob Smith',
    openedAt: '2024-02-24T08:00:00Z',
    shiftId: 'SHIFT002',
  },
  {
    id: 'REG003',
    name: 'Bar Register',
    location: 'Casino Bar',
    status: 'closed',
    currentBalance: 0,
    openingBalance: 500.0,
    employeeId: 'EMP005',
    employeeName: 'Eva Martinez',
    closedAt: '2024-02-23T23:30:00Z',
  },
];

// Mock Transactions Data
export const mockTransactions: Transaction[] = [
  {
    id: 'TXN001',
    registerId: 'REG001',
    type: 'sale',
    amount: 150.0,
    description: 'Slot machine payout',
    employeeId: 'EMP003',
    employeeName: 'Carol Davis',
    timestamp: '2024-02-24T10:30:00Z',
    orderId: 'ORD001',
    receiptNumber: 'RCP001',
  },
  {
    id: 'TXN002',
    registerId: 'REG002',
    type: 'sale',
    amount: 500.0,
    description: 'Table game chips',
    employeeId: 'EMP002',
    employeeName: 'Bob Smith',
    timestamp: '2024-02-24T11:15:00Z',
    receiptNumber: 'RCP002',
  },
  {
    id: 'TXN003',
    registerId: 'REG001',
    type: 'refund',
    amount: -25.0,
    description: 'Drink refund',
    employeeId: 'EMP003',
    employeeName: 'Carol Davis',
    timestamp: '2024-02-24T12:00:00Z',
    receiptNumber: 'RCP003',
  },
  {
    id: 'TXN004',
    registerId: 'REG002',
    type: 'cash_in',
    amount: 1000.0,
    description: 'Cash deposit',
    employeeId: 'EMP002',
    employeeName: 'Bob Smith',
    timestamp: '2024-02-24T09:00:00Z',
  },
];

// Mock Shifts Data
export const mockShifts: Shift[] = [
  {
    id: 'SHIFT001',
    registerId: 'REG001',
    employeeId: 'EMP003',
    employeeName: 'Carol Davis',
    startTime: '2024-02-24T08:00:00Z',
    openingBalance: 1000.0,
    totalSales: 1500.0,
    totalRefunds: 25.0,
    status: 'active',
  },
  {
    id: 'SHIFT002',
    registerId: 'REG002',
    employeeId: 'EMP002',
    employeeName: 'Bob Smith',
    startTime: '2024-02-24T08:00:00Z',
    openingBalance: 2000.0,
    totalSales: 3200.0,
    totalRefunds: 0,
    status: 'active',
  },
  {
    id: 'SHIFT003',
    registerId: 'REG003',
    employeeId: 'EMP005',
    employeeName: 'Eva Martinez',
    startTime: '2024-02-23T16:00:00Z',
    endTime: '2024-02-23T23:30:00Z',
    openingBalance: 500.0,
    closingBalance: 750.0,
    totalSales: 250.0,
    totalRefunds: 0,
    status: 'completed',
  },
];

// Mock Stores Data
export const mockStores: Store[] = [
  {
    id: 'STORE001',
    name: 'Galaxy Main Casino',
    location: 'Downtown',
    address: '123 Casino Blvd, Las Vegas, NV 89101',
    phone: '******-1000',
    email: '<EMAIL>',
    managerId: 'EMP001',
    managerName: 'Alice Johnson',
    status: 'active',
    openingHours: {
      monday: { open: '10:00', close: '02:00' },
      tuesday: { open: '10:00', close: '02:00' },
      wednesday: { open: '10:00', close: '02:00' },
      thursday: { open: '10:00', close: '02:00' },
      friday: { open: '10:00', close: '04:00' },
      saturday: { open: '10:00', close: '04:00' },
      sunday: { open: '12:00', close: '02:00' },
    },
    totalRevenue: 2500000,
    monthlyRevenue: 450000,
    employeeCount: 45,
  },
  {
    id: 'STORE002',
    name: 'Galaxy Express',
    location: 'Airport',
    address: '456 Terminal Way, Las Vegas, NV 89119',
    phone: '******-2000',
    email: '<EMAIL>',
    managerId: 'EMP002',
    managerName: 'Bob Smith',
    status: 'active',
    openingHours: {
      monday: { open: '06:00', close: '23:00' },
      tuesday: { open: '06:00', close: '23:00' },
      wednesday: { open: '06:00', close: '23:00' },
      thursday: { open: '06:00', close: '23:00' },
      friday: { open: '06:00', close: '23:00' },
      saturday: { open: '06:00', close: '23:00' },
      sunday: { open: '06:00', close: '23:00' },
    },
    totalRevenue: 850000,
    monthlyRevenue: 125000,
    employeeCount: 12,
  },
  {
    id: 'STORE003',
    name: 'Galaxy Resort',
    location: 'Strip',
    address: '789 Las Vegas Blvd, Las Vegas, NV 89109',
    phone: '******-3000',
    email: '<EMAIL>',
    managerId: 'EMP001',
    managerName: 'Alice Johnson',
    status: 'maintenance',
    openingHours: {
      monday: { open: '00:00', close: '23:59' },
      tuesday: { open: '00:00', close: '23:59' },
      wednesday: { open: '00:00', close: '23:59' },
      thursday: { open: '00:00', close: '23:59' },
      friday: { open: '00:00', close: '23:59' },
      saturday: { open: '00:00', close: '23:59' },
      sunday: { open: '00:00', close: '23:59' },
    },
    totalRevenue: 5200000,
    monthlyRevenue: 890000,
    employeeCount: 78,
  },
];

// Mock Products Data
export const mockProducts: Product[] = [
  {
    id: 'PROD001',
    name: 'Poker Chips - $5',
    category: 'Gaming',
    price: 5.0,
    cost: 0.5,
    stockLevel: 2500,
    minStockLevel: 500,
    storeId: 'STORE001',
    status: 'active',
    lastRestocked: '2024-02-20T10:00:00Z',
  },
  {
    id: 'PROD002',
    name: 'Premium Whiskey',
    category: 'Beverages',
    price: 45.0,
    cost: 18.0,
    stockLevel: 25,
    minStockLevel: 50,
    storeId: 'STORE001',
    status: 'active',
    lastRestocked: '2024-02-15T14:30:00Z',
  },
  {
    id: 'PROD003',
    name: 'Playing Cards - Deck',
    category: 'Gaming',
    price: 8.0,
    cost: 2.0,
    stockLevel: 150,
    minStockLevel: 100,
    storeId: 'STORE002',
    status: 'active',
    lastRestocked: '2024-02-22T09:15:00Z',
  },
  {
    id: 'PROD004',
    name: 'Slot Machine Tokens',
    category: 'Gaming',
    price: 1.0,
    cost: 0.1,
    stockLevel: 45,
    minStockLevel: 1000,
    storeId: 'STORE003',
    status: 'active',
    lastRestocked: '2024-02-10T16:00:00Z',
  },
];

// Mock Management Stats
export const mockManagementStats: ManagementStats = {
  totalEmployees: mockEmployees.length,
  activeEmployees: mockEmployees.filter((emp) => emp.status === 'active')
    .length,
  totalStores: mockStores.length,
  activeStores: mockStores.filter((store) => store.status === 'active').length,
  totalRegisters: mockCashRegisters.length,
  openRegisters: mockCashRegisters.filter((reg) => reg.status === 'open')
    .length,
  todayRevenue: mockTransactions
    .filter(
      (txn) =>
        txn.type === 'sale' &&
        new Date(txn.timestamp).toDateString() === new Date().toDateString(),
    )
    .reduce((sum, txn) => sum + txn.amount, 0),
  monthlyRevenue: mockStores.reduce(
    (sum, store) => sum + store.monthlyRevenue,
    0,
  ),
  lowStockItems: mockProducts.filter(
    (product) => product.stockLevel < product.minStockLevel,
  ).length,
  pendingShifts: mockShifts.filter((shift) => shift.status === 'active').length,
};

// Mock Exchange Rates
export const mockExchangeRates: Array<ExchangeRate> = [
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'TWD',
    spotPrice: 29.91,
    askPrice: 30.01,
    bidPrice: 29.81,
    updatedAt: '2025-05-25T10:30:00Z',
    updateNote: '',
    autoUpdate: true,
    autoUpdateSettings: {
      frequency: 'hourly',
    },
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'HKD',
    spotPrice: 7.84,
    askPrice: 7.94,
    bidPrice: 7.74,
    updatedAt: '2025-05-25T09:00:00Z',
    updateNote: '',
    autoUpdate: false,
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'JPY',
    spotPrice: 142.53,
    askPrice: 142.63,
    bidPrice: 142.43,
    updatedAt: '2025-05-24T22:45:00Z',
    updateNote: '',
    autoUpdate: true,
    autoUpdateSettings: {
      frequency: 'daily',
      time: '08:30',
    },
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'CNY',
    spotPrice: 7.18,
    askPrice: 7.28,
    bidPrice: 7.08,
    updatedAt: '2025-05-25T01:00:00Z',
    updateNote: '',
    autoUpdate: false,
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'SGD',
    spotPrice: 1.28,
    askPrice: 1.38,
    bidPrice: 1.18,
    updatedAt: '2025-05-25T03:15:00Z',
    updateNote: '',
    autoUpdate: true,
    autoUpdateSettings: {
      frequency: 'weekly',
      day: 'mon',
      time: '12:00',
    },
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'MYR',
    spotPrice: 4.21,
    askPrice: 4.31,
    bidPrice: 4.11,
    updatedAt: '2025-05-24T20:00:00Z',
    updateNote: '',
    autoUpdate: false,
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'KRW',
    spotPrice: 1364.35,
    askPrice: 1364.45,
    bidPrice: 1364.25,
    updatedAt: '2025-05-25T11:00:00Z',
    updateNote: '',
    autoUpdate: true,
    autoUpdateSettings: {
      frequency: 'hourly',
    },
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'THB',
    spotPrice: 32.43,
    askPrice: 32.53,
    bidPrice: 32.33,
    updatedAt: '2025-05-24T18:30:00Z',
    updateNote: '',
    autoUpdate: false,
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'VND',
    spotPrice: 25916,
    askPrice: 25916.1,
    bidPrice: 25915.9,
    updatedAt: '2025-05-25T07:45:00Z',
    updateNote: '',
    autoUpdate: true,
    autoUpdateSettings: {
      frequency: 'daily',
      time: '07:30',
    },
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'USD',
    spotPrice: 1.0,
    askPrice: 1.1,
    bidPrice: 0.9,
    updatedAt: '2025-05-25T12:00:00Z',
    updateNote: '',
    autoUpdate: false,
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'GBP',
    spotPrice: 0.736649,
    askPrice: 0.836649,
    bidPrice: 0.636649,
    updatedAt: '2025-05-25T08:20:00Z',
    updateNote: '',
    autoUpdate: true,
    autoUpdateSettings: {
      frequency: 'weekly',
      day: 'sun',
      time: '09:00',
    },
  },
  {
    cryptoSymbol: 'USDT',
    fiatCode: 'PHP',
    spotPrice: 55.34,
    askPrice: 55.44,
    bidPrice: 55.24,
    updatedAt: '2025-05-25T06:10:00Z',
    updateNote: '',
    autoUpdate: false,
  },
];

// Mock Permission Categories
export const mockPermissionCategories: PermissionCategory[] = [
  {
    id: 'user_management',
    name: 'User Management',
    description: 'Manage users, employees, and member accounts',
    icon: 'UserOutlined',
  },
  {
    id: 'financial',
    name: 'Financial Operations',
    description: 'Handle transactions, payments, and financial data',
    icon: 'DollarOutlined',
  },
  {
    id: 'gaming',
    name: 'Gaming Operations',
    description: 'Manage gaming floor, machines, and activities',
    icon: 'TrophyOutlined',
  },
  {
    id: 'security',
    name: 'Security & Monitoring',
    description: 'Security operations and incident management',
    icon: 'SafetyOutlined',
  },
  {
    id: 'reporting',
    name: 'Reports & Analytics',
    description: 'Access reports, analytics, and business intelligence',
    icon: 'BarChartOutlined',
  },
  {
    id: 'system',
    name: 'System Administration',
    description: 'System settings, configurations, and maintenance',
    icon: 'SettingOutlined',
  },
];

// Mock Permissions
export const mockPermissions: Permission[] = [
  // User Management Permissions
  {
    id: 'users_view',
    name: 'View Users',
    description: 'View user accounts and basic information',
    category: mockPermissionCategories[0],
    actions: [
      {
        id: 'users_view_list',
        name: 'View User List',
        description: 'Access user listing page',
        type: 'view',
      },
      {
        id: 'users_view_details',
        name: 'View User Details',
        description: 'View detailed user information',
        type: 'view',
      },
    ],
  },
  {
    id: 'users_manage',
    name: 'Manage Users',
    description: 'Create, edit, and manage user accounts',
    category: mockPermissionCategories[0],
    actions: [
      {
        id: 'users_create',
        name: 'Create Users',
        description: 'Create new user accounts',
        type: 'create',
      },
      {
        id: 'users_edit',
        name: 'Edit Users',
        description: 'Modify user account information',
        type: 'edit',
      },
      {
        id: 'users_delete',
        name: 'Delete Users',
        description: 'Delete user accounts',
        type: 'delete',
      },
      {
        id: 'users_status',
        name: 'Manage User Status',
        description: 'Activate/deactivate user accounts',
        type: 'manage',
      },
    ],
  },
  {
    id: 'employees_view',
    name: 'View Employees',
    description: 'View employee information and records',
    category: mockPermissionCategories[0],
    actions: [
      {
        id: 'employees_view_list',
        name: 'View Employee List',
        description: 'Access employee listing',
        type: 'view',
      },
      {
        id: 'employees_view_details',
        name: 'View Employee Details',
        description: 'View detailed employee information',
        type: 'view',
      },
      {
        id: 'employees_view_attendance',
        name: 'View Attendance',
        description: 'View employee attendance records',
        type: 'view',
      },
    ],
  },
  {
    id: 'employees_manage',
    name: 'Manage Employees',
    description: 'Full employee management capabilities',
    category: mockPermissionCategories[0],
    actions: [
      {
        id: 'employees_create',
        name: 'Create Employees',
        description: 'Add new employees',
        type: 'create',
      },
      {
        id: 'employees_edit',
        name: 'Edit Employees',
        description: 'Modify employee information',
        type: 'edit',
      },
      {
        id: 'employees_delete',
        name: 'Delete Employees',
        description: 'Remove employee records',
        type: 'delete',
      },
      {
        id: 'employees_schedule',
        name: 'Manage Schedules',
        description: 'Create and modify employee schedules',
        type: 'manage',
      },
    ],
  },
  {
    id: 'members_view',
    name: 'View Members',
    description: 'View casino member information',
    category: mockPermissionCategories[0],
    actions: [
      {
        id: 'members_view_list',
        name: 'View Member List',
        description: 'Access member listing',
        type: 'view',
      },
      {
        id: 'members_view_details',
        name: 'View Member Details',
        description: 'View detailed member information',
        type: 'view',
      },
    ],
  },
  {
    id: 'members_manage',
    name: 'Manage Members',
    description: 'Full member management capabilities',
    category: mockPermissionCategories[0],
    actions: [
      {
        id: 'members_create',
        name: 'Create Members',
        description: 'Register new members',
        type: 'create',
      },
      {
        id: 'members_edit',
        name: 'Edit Members',
        description: 'Modify member information',
        type: 'edit',
      },
      {
        id: 'members_status',
        name: 'Manage Member Status',
        description: 'Activate/deactivate member accounts',
        type: 'manage',
      },
    ],
  },
  // Financial Permissions
  {
    id: 'transactions_view',
    name: 'View Transactions',
    description: 'View financial transactions and payment records',
    category: mockPermissionCategories[1],
    actions: [
      {
        id: 'transactions_view_list',
        name: 'View Transaction List',
        description: 'Access transaction history',
        type: 'view',
      },
      {
        id: 'transactions_view_details',
        name: 'View Transaction Details',
        description: 'View detailed transaction information',
        type: 'view',
      },
    ],
  },
  {
    id: 'transactions_manage',
    name: 'Manage Transactions',
    description: 'Process and manage financial transactions',
    category: mockPermissionCategories[1],
    actions: [
      {
        id: 'transactions_create',
        name: 'Create Transactions',
        description: 'Process new transactions',
        type: 'create',
      },
      {
        id: 'transactions_refund',
        name: 'Process Refunds',
        description: 'Handle transaction refunds',
        type: 'manage',
      },
      {
        id: 'transactions_void',
        name: 'Void Transactions',
        description: 'Cancel transactions',
        type: 'manage',
      },
    ],
  },
  {
    id: 'cash_register',
    name: 'Cash Register Operations',
    description: 'Operate cash registers and handle cash',
    category: mockPermissionCategories[1],
    actions: [
      {
        id: 'register_open',
        name: 'Open Register',
        description: 'Open cash register for shift',
        type: 'manage',
      },
      {
        id: 'register_close',
        name: 'Close Register',
        description: 'Close cash register and reconcile',
        type: 'manage',
      },
      {
        id: 'register_view',
        name: 'View Register Status',
        description: 'Check register status and balance',
        type: 'view',
      },
    ],
  },
  // Gaming Permissions
  {
    id: 'gaming_floor',
    name: 'Gaming Floor Management',
    description: 'Manage gaming floor operations and activities',
    category: mockPermissionCategories[2],
    actions: [
      {
        id: 'gaming_view',
        name: 'View Gaming Activities',
        description: 'Monitor gaming floor activities',
        type: 'view',
      },
      {
        id: 'gaming_manage',
        name: 'Manage Gaming Operations',
        description: 'Control gaming floor operations',
        type: 'manage',
      },
    ],
  },
  {
    id: 'orders_view',
    name: 'View Orders',
    description: 'View customer orders and purchases',
    category: mockPermissionCategories[2],
    actions: [
      {
        id: 'orders_view_list',
        name: 'View Order List',
        description: 'Access order history',
        type: 'view',
      },
      {
        id: 'orders_view_details',
        name: 'View Order Details',
        description: 'View detailed order information',
        type: 'view',
      },
    ],
  },
  {
    id: 'orders_manage',
    name: 'Manage Orders',
    description: 'Process and manage customer orders',
    category: mockPermissionCategories[2],
    actions: [
      {
        id: 'orders_create',
        name: 'Create Orders',
        description: 'Process new orders',
        type: 'create',
      },
      {
        id: 'orders_edit',
        name: 'Edit Orders',
        description: 'Modify existing orders',
        type: 'edit',
      },
      {
        id: 'orders_cancel',
        name: 'Cancel Orders',
        description: 'Cancel customer orders',
        type: 'manage',
      },
    ],
  },
  // Security Permissions
  {
    id: 'security_monitor',
    name: 'Security Monitoring',
    description: 'Monitor security systems and incidents',
    category: mockPermissionCategories[3],
    actions: [
      {
        id: 'security_view',
        name: 'View Security Status',
        description: 'Monitor security systems',
        type: 'view',
      },
      {
        id: 'security_incidents',
        name: 'Manage Incidents',
        description: 'Handle security incidents',
        type: 'manage',
      },
    ],
  },
  // Reporting Permissions
  {
    id: 'reports_view',
    name: 'View Reports',
    description: 'Access business reports and analytics',
    category: mockPermissionCategories[4],
    actions: [
      {
        id: 'reports_financial',
        name: 'Financial Reports',
        description: 'View financial reports',
        type: 'view',
      },
      {
        id: 'reports_operational',
        name: 'Operational Reports',
        description: 'View operational reports',
        type: 'view',
      },
      {
        id: 'reports_analytics',
        name: 'Analytics Dashboard',
        description: 'Access analytics and insights',
        type: 'view',
      },
    ],
  },
  {
    id: 'reports_export',
    name: 'Export Reports',
    description: 'Export reports and data',
    category: mockPermissionCategories[4],
    actions: [
      {
        id: 'reports_export_pdf',
        name: 'Export PDF',
        description: 'Export reports as PDF',
        type: 'manage',
      },
      {
        id: 'reports_export_excel',
        name: 'Export Excel',
        description: 'Export data to Excel',
        type: 'manage',
      },
    ],
  },
  // System Permissions
  {
    id: 'system_settings',
    name: 'System Settings',
    description: 'Manage system configurations and settings',
    category: mockPermissionCategories[5],
    actions: [
      {
        id: 'settings_view',
        name: 'View Settings',
        description: 'View system settings',
        type: 'view',
      },
      {
        id: 'settings_edit',
        name: 'Edit Settings',
        description: 'Modify system settings',
        type: 'edit',
      },
    ],
  },
  {
    id: 'roles_permissions',
    name: 'Roles & Permissions',
    description: 'Manage user roles and permissions',
    category: mockPermissionCategories[5],
    actions: [
      {
        id: 'roles_view',
        name: 'View Roles',
        description: 'View role definitions',
        type: 'view',
      },
      {
        id: 'roles_create',
        name: 'Create Roles',
        description: 'Create new roles',
        type: 'create',
      },
      {
        id: 'roles_edit',
        name: 'Edit Roles',
        description: 'Modify role permissions',
        type: 'edit',
      },
      {
        id: 'roles_delete',
        name: 'Delete Roles',
        description: 'Remove roles',
        type: 'delete',
      },
      {
        id: 'roles_assign',
        name: 'Assign Roles',
        description: 'Assign roles to users',
        type: 'manage',
      },
    ],
  },
];

// Mock Roles
export const mockRoles: Role[] = [
  {
    id: 'super_admin',
    name: 'Super Administrator',
    description: 'Full system access with all permissions',
    status: 'active',
    permissions: mockPermissions.map((p) => p.id), // All permissions
    userCount: 1,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-01T00:00:00Z',
    createdBy: 'system',
  },
  {
    id: 'admin',
    name: 'Administrator',
    description: 'Administrative access with most permissions',
    status: 'active',
    permissions: [
      'users_view',
      'users_manage',
      'employees_view',
      'employees_manage',
      'members_view',
      'members_manage',
      'transactions_view',
      'transactions_manage',
      'orders_view',
      'orders_manage',
      'reports_view',
      'reports_export',
      'system_settings',
    ],
    userCount: 2,
    isSystem: true,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-02-15T00:00:00Z',
    createdBy: 'super_admin',
  },
  {
    id: 'manager',
    name: 'Manager',
    description: 'Management level access for daily operations',
    status: 'active',
    permissions: [
      'employees_view',
      'employees_manage',
      'members_view',
      'members_manage',
      'transactions_view',
      'orders_view',
      'orders_manage',
      'gaming_floor',
      'reports_view',
      'cash_register',
    ],
    userCount: 5,
    isSystem: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-02-10T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'cashier',
    name: 'Cashier',
    description: 'Cash handling and transaction processing',
    status: 'active',
    permissions: [
      'members_view',
      'transactions_view',
      'transactions_manage',
      'orders_view',
      'orders_manage',
      'cash_register',
    ],
    userCount: 12,
    isSystem: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-02-05T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'security',
    name: 'Security Officer',
    description: 'Security monitoring and incident management',
    status: 'active',
    permissions: ['members_view', 'security_monitor', 'reports_view'],
    userCount: 8,
    isSystem: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-20T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'maintenance',
    name: 'Maintenance Staff',
    description: 'Equipment maintenance and facility management',
    status: 'active',
    permissions: ['system_settings'],
    userCount: 4,
    isSystem: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-01-15T00:00:00Z',
    createdBy: 'admin',
  },
  {
    id: 'viewer',
    name: 'Viewer',
    description: 'Read-only access to basic information',
    status: 'inactive',
    permissions: ['members_view', 'orders_view', 'reports_view'],
    userCount: 0,
    isSystem: false,
    createdAt: '2024-01-01T00:00:00Z',
    updatedAt: '2024-02-01T00:00:00Z',
    createdBy: 'admin',
  },
];

// Mock User Roles (assignments)
export const mockUserRoles: UserRole[] = [
  {
    id: 'ur_001',
    userId: 'EMP001',
    userName: 'Alice Johnson',
    userEmail: '<EMAIL>',
    roleId: 'super_admin',
    roleName: 'Super Administrator',
    assignedAt: '2024-01-01T00:00:00Z',
    assignedBy: 'system',
  },
  {
    id: 'ur_002',
    userId: 'EMP002',
    userName: 'Bob Smith',
    userEmail: '<EMAIL>',
    roleId: 'manager',
    roleName: 'Manager',
    assignedAt: '2024-01-15T00:00:00Z',
    assignedBy: 'EMP001',
  },
  {
    id: 'ur_003',
    userId: 'EMP003',
    userName: 'Carol Davis',
    userEmail: '<EMAIL>',
    roleId: 'cashier',
    roleName: 'Cashier',
    assignedAt: '2024-01-20T00:00:00Z',
    assignedBy: 'EMP001',
  },
  {
    id: 'ur_004',
    userId: 'EMP004',
    userName: 'David Wilson',
    userEmail: '<EMAIL>',
    roleId: 'security',
    roleName: 'Security Officer',
    assignedAt: '2024-01-25T00:00:00Z',
    assignedBy: 'EMP001',
  },
  {
    id: 'ur_005',
    userId: 'EMP005',
    userName: 'Eva Martinez',
    userEmail: '<EMAIL>',
    roleId: 'maintenance',
    roleName: 'Maintenance Staff',
    assignedAt: '2024-02-01T00:00:00Z',
    assignedBy: 'EMP002',
  },
];

// Mock Financial Statements Data
export const mockFinancialStatements: FinancialStatement[] = [
  {
    id: 'FS_202402',
    type: 'income_statement',
    period: {
      startDate: '2024-02-01T00:00:00Z',
      endDate: '2024-02-29T23:59:59Z',
      type: 'monthly',
    },
    data: {
      revenue: {
        gaming: 850000,
        food: 125000,
        beverages: 85000,
        other: 45000,
        total: 1105000,
      },
      expenses: {
        salaries: 280000,
        utilities: 45000,
        maintenance: 35000,
        supplies: 25000,
        marketing: 15000,
        other: 20000,
        total: 420000,
      },
      assets: {
        cash: 450000,
        inventory: 85000,
        equipment: 1200000,
        other: 65000,
        total: 1800000,
      },
      liabilities: {
        accountsPayable: 125000,
        loans: 800000,
        other: 75000,
        total: 1000000,
      },
      equity: {
        capital: 500000,
        retainedEarnings: 300000,
        total: 800000,
      },
    },
    generatedAt: '2024-03-01T09:00:00Z',
    generatedBy: 'EMP001',
  },
  {
    id: 'FS_202401',
    type: 'income_statement',
    period: {
      startDate: '2024-01-01T00:00:00Z',
      endDate: '2024-01-31T23:59:59Z',
      type: 'monthly',
    },
    data: {
      revenue: {
        gaming: 920000,
        food: 135000,
        beverages: 92000,
        other: 53000,
        total: 1200000,
      },
      expenses: {
        salaries: 285000,
        utilities: 48000,
        maintenance: 32000,
        supplies: 28000,
        marketing: 18000,
        other: 24000,
        total: 435000,
      },
      assets: {
        cash: 520000,
        inventory: 92000,
        equipment: 1180000,
        other: 58000,
        total: 1850000,
      },
      liabilities: {
        accountsPayable: 115000,
        loans: 820000,
        other: 65000,
        total: 1000000,
      },
      equity: {
        capital: 500000,
        retainedEarnings: 350000,
        total: 850000,
      },
    },
    generatedAt: '2024-02-01T09:00:00Z',
    generatedBy: 'EMP001',
  },
];

// Mock Transaction Summaries
export const mockTransactionSummaries: TransactionSummary[] = [
  {
    id: 'TS_20240224',
    period: {
      startDate: '2024-02-24T00:00:00Z',
      endDate: '2024-02-24T23:59:59Z',
    },
    summary: {
      totalTransactions: 156,
      totalAmount: 45680,
      averageTransaction: 292.82,
      byType: {
        sale: { count: 142, amount: 48200 },
        refund: { count: 8, amount: -2850 },
        void: { count: 3, amount: -1200 },
        cash_in: { count: 2, amount: 1000 },
        cash_out: { count: 1, amount: 530 },
      },
      byPaymentMethod: {
        cash: { count: 89, amount: 28450 },
        card: { count: 52, amount: 15680 },
        digital_wallet: { count: 12, amount: 1350 },
        crypto: { count: 3, amount: 200 },
      },
      byEmployee: {
        EMP002: { employeeName: 'Bob Smith', count: 45, amount: 13250 },
        EMP003: { employeeName: 'Carol Davis', count: 67, amount: 19850 },
        EMP004: { employeeName: 'David Wilson', count: 44, amount: 12580 },
      },
      byRegister: {
        REG001: { registerName: 'Main Counter', count: 89, amount: 26450 },
        REG002: { registerName: 'VIP Lounge', count: 45, amount: 15680 },
        REG003: { registerName: 'Gaming Floor', count: 22, amount: 3550 },
      },
    },
    transactions: {
      sale: [
        {
          id: 'TXN_001',
          timestamp: '2024-02-24T09:15:00Z',
          amount: 250,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Gaming chips purchase',
          reference: 'REF_001',
        },
        {
          id: 'TXN_002',
          timestamp: '2024-02-24T09:32:00Z',
          amount: 150,
          paymentMethod: 'card',
          employeeId: 'EMP003',
          employeeName: 'Carol Davis',
          registerId: 'REG002',
          registerName: 'VIP Lounge',
          description: 'Food and beverage',
          reference: 'REF_002',
        },
        {
          id: 'TXN_003',
          timestamp: '2024-02-24T10:45:00Z',
          amount: 500,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'High roller gaming',
          reference: 'REF_003',
        },
        {
          id: 'TXN_004',
          timestamp: '2024-02-24T11:20:00Z',
          amount: 75,
          paymentMethod: 'digital_wallet',
          employeeId: 'EMP004',
          employeeName: 'David Wilson',
          registerId: 'REG003',
          registerName: 'Gaming Floor',
          description: 'Slot machine credits',
          reference: 'REF_004',
        },
        {
          id: 'TXN_005',
          timestamp: '2024-02-24T14:30:00Z',
          amount: 320,
          paymentMethod: 'card',
          employeeId: 'EMP003',
          employeeName: 'Carol Davis',
          registerId: 'REG002',
          registerName: 'VIP Lounge',
          description: 'VIP gaming package',
          reference: 'REF_005',
        },
      ],
      refund: [
        {
          id: 'TXN_R001',
          timestamp: '2024-02-24T15:45:00Z',
          amount: -120,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Unused gaming chips refund',
          reference: 'REF_R001',
          originalTransactionId: 'TXN_001',
        },
        {
          id: 'TXN_R002',
          timestamp: '2024-02-24T16:20:00Z',
          amount: -85,
          paymentMethod: 'card',
          employeeId: 'EMP003',
          employeeName: 'Carol Davis',
          registerId: 'REG002',
          registerName: 'VIP Lounge',
          description: 'Food order cancellation',
          reference: 'REF_R002',
          originalTransactionId: 'TXN_002',
        },
      ],
      void: [
        {
          id: 'TXN_V001',
          timestamp: '2024-02-24T12:10:00Z',
          amount: -200,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Transaction error - voided',
          reference: 'REF_V001',
          reason: 'System error during processing',
        },
      ],
      cash_in: [
        {
          id: 'TXN_CI001',
          timestamp: '2024-02-24T08:00:00Z',
          amount: 5000,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Opening cash float',
          reference: 'REF_CI001',
        },
      ],
      cash_out: [
        {
          id: 'TXN_CO001',
          timestamp: '2024-02-24T18:00:00Z',
          amount: -2000,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Cash drop to safe',
          reference: 'REF_CO001',
        },
      ],
    },
    transactionsByPaymentMethod: {
      cash: [
        {
          id: 'TXN_001',
          timestamp: '2024-02-24T09:15:00Z',
          amount: 250,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Gaming chips purchase',
          reference: 'REF_001',
        },
        {
          id: 'TXN_003',
          timestamp: '2024-02-24T10:45:00Z',
          amount: 500,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'High roller gaming',
          reference: 'REF_003',
        },
        {
          id: 'TXN_CI001',
          timestamp: '2024-02-24T08:00:00Z',
          amount: 5000,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Opening cash float',
          reference: 'REF_CI001',
        },
      ],
      card: [
        {
          id: 'TXN_002',
          timestamp: '2024-02-24T09:32:00Z',
          amount: 150,
          paymentMethod: 'card',
          employeeId: 'EMP003',
          employeeName: 'Carol Davis',
          registerId: 'REG002',
          registerName: 'VIP Lounge',
          description: 'Food and beverage',
          reference: 'REF_002',
        },
        {
          id: 'TXN_005',
          timestamp: '2024-02-24T14:30:00Z',
          amount: 320,
          paymentMethod: 'card',
          employeeId: 'EMP003',
          employeeName: 'Carol Davis',
          registerId: 'REG002',
          registerName: 'VIP Lounge',
          description: 'VIP gaming package',
          reference: 'REF_005',
        },
      ],
      digital_wallet: [
        {
          id: 'TXN_004',
          timestamp: '2024-02-24T11:20:00Z',
          amount: 75,
          paymentMethod: 'digital_wallet',
          employeeId: 'EMP004',
          employeeName: 'David Wilson',
          registerId: 'REG003',
          registerName: 'Gaming Floor',
          description: 'Slot machine credits',
          reference: 'REF_004',
        },
      ],
    },
    transactionsByEmployee: {
      EMP002: [
        {
          id: 'TXN_001',
          timestamp: '2024-02-24T09:15:00Z',
          amount: 250,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Gaming chips purchase',
          reference: 'REF_001',
        },
        {
          id: 'TXN_003',
          timestamp: '2024-02-24T10:45:00Z',
          amount: 500,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'High roller gaming',
          reference: 'REF_003',
        },
        {
          id: 'TXN_CI001',
          timestamp: '2024-02-24T08:00:00Z',
          amount: 5000,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Opening cash float',
          reference: 'REF_CI001',
        },
      ],
      EMP003: [
        {
          id: 'TXN_002',
          timestamp: '2024-02-24T09:32:00Z',
          amount: 150,
          paymentMethod: 'card',
          employeeId: 'EMP003',
          employeeName: 'Carol Davis',
          registerId: 'REG002',
          registerName: 'VIP Lounge',
          description: 'Food and beverage',
          reference: 'REF_002',
        },
        {
          id: 'TXN_005',
          timestamp: '2024-02-24T14:30:00Z',
          amount: 320,
          paymentMethod: 'card',
          employeeId: 'EMP003',
          employeeName: 'Carol Davis',
          registerId: 'REG002',
          registerName: 'VIP Lounge',
          description: 'VIP gaming package',
          reference: 'REF_005',
        },
      ],
      EMP004: [
        {
          id: 'TXN_004',
          timestamp: '2024-02-24T11:20:00Z',
          amount: 75,
          paymentMethod: 'digital_wallet',
          employeeId: 'EMP004',
          employeeName: 'David Wilson',
          registerId: 'REG003',
          registerName: 'Gaming Floor',
          description: 'Slot machine credits',
          reference: 'REF_004',
        },
      ],
    },
    transactionsByRegister: {
      REG001: [
        {
          id: 'TXN_001',
          timestamp: '2024-02-24T09:15:00Z',
          amount: 250,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Gaming chips purchase',
          reference: 'REF_001',
        },
        {
          id: 'TXN_003',
          timestamp: '2024-02-24T10:45:00Z',
          amount: 500,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'High roller gaming',
          reference: 'REF_003',
        },
        {
          id: 'TXN_CI001',
          timestamp: '2024-02-24T08:00:00Z',
          amount: 5000,
          paymentMethod: 'cash',
          employeeId: 'EMP002',
          employeeName: 'Bob Smith',
          registerId: 'REG001',
          registerName: 'Main Counter',
          description: 'Opening cash float',
          reference: 'REF_CI001',
        },
      ],
      REG002: [
        {
          id: 'TXN_002',
          timestamp: '2024-02-24T09:32:00Z',
          amount: 150,
          paymentMethod: 'card',
          employeeId: 'EMP003',
          employeeName: 'Carol Davis',
          registerId: 'REG002',
          registerName: 'VIP Lounge',
          description: 'Food and beverage',
          reference: 'REF_002',
        },
        {
          id: 'TXN_005',
          timestamp: '2024-02-24T14:30:00Z',
          amount: 320,
          paymentMethod: 'card',
          employeeId: 'EMP003',
          employeeName: 'Carol Davis',
          registerId: 'REG002',
          registerName: 'VIP Lounge',
          description: 'VIP gaming package',
          reference: 'REF_005',
        },
      ],
      REG003: [
        {
          id: 'TXN_004',
          timestamp: '2024-02-24T11:20:00Z',
          amount: 75,
          paymentMethod: 'digital_wallet',
          employeeId: 'EMP004',
          employeeName: 'David Wilson',
          registerId: 'REG003',
          registerName: 'Gaming Floor',
          description: 'Slot machine credits',
          reference: 'REF_004',
        },
      ],
    },
    generatedAt: '2024-02-25T08:00:00Z',
  },
];

// Mock Reconciliation Reports
export const mockReconciliationReports: ReconciliationReport[] = [
  {
    id: 'REC_20240224_REG001',
    registerId: 'REG001',
    registerName: 'Main Counter',
    shiftId: 'SHIFT_20240224_001',
    period: {
      startDate: '2024-02-24T08:00:00Z',
      endDate: '2024-02-24T16:00:00Z',
    },
    openingBalance: 5000,
    closingBalance: 5000,
    expectedBalance: 18450,
    actualBalance: 18425,
    variance: -25,
    transactions: {
      sales: 15680,
      refunds: -1250,
      cashIn: 500,
      cashOut: -1480,
      voids: -0,
    },
    discrepancies: [
      {
        type: 'shortage',
        amount: 25,
        reason: 'Minor counting discrepancy',
      },
    ],
    status: 'variance',
    reconciledBy: 'EMP002',
    reconciledAt: '2024-02-24T16:15:00Z',
    notes: 'Small variance within acceptable range',
  },
  {
    id: 'REC_20240224_REG002',
    registerId: 'REG002',
    registerName: 'VIP Lounge',
    shiftId: 'SHIFT_20240224_002',
    period: {
      startDate: '2024-02-24T08:00:00Z',
      endDate: '2024-02-24T16:00:00Z',
    },
    openingBalance: 10000,
    closingBalance: 10000,
    expectedBalance: 25680,
    actualBalance: 25680,
    variance: 0,
    transactions: {
      sales: 18450,
      refunds: -850,
      cashIn: 0,
      cashOut: -1920,
      voids: 0,
    },
    discrepancies: [],
    status: 'balanced',
    reconciledBy: 'EMP003',
    reconciledAt: '2024-02-24T16:05:00Z',
    notes: 'Perfect balance - no discrepancies',
  },
  {
    id: 'REC_20240223_REG001',
    registerId: 'REG001',
    registerName: 'Main Counter',
    period: {
      startDate: '2024-02-23T08:00:00Z',
      endDate: '2024-02-23T16:00:00Z',
    },
    openingBalance: 5000,
    closingBalance: 5000,
    expectedBalance: 16850,
    actualBalance: 16900,
    variance: 50,
    transactions: {
      sales: 14250,
      refunds: -950,
      cashIn: 1000,
      cashOut: -1450,
      voids: 0,
    },
    discrepancies: [
      {
        type: 'overage',
        amount: 50,
        reason: 'Customer overpayment not processed as change',
      },
    ],
    status: 'variance',
    reconciledBy: 'EMP001',
    reconciledAt: '2024-02-23T16:20:00Z',
    notes: 'Overage returned to customer next day',
  },
];

export const mockAdminAccount: LoginProps = {
  username: 'admin',
  password: '123456aA',
};

export const mockLoginRes: LoginRes = {
  token: 'fake-token',
  userId: '1',
  email: '<EMAIL>',
  phoneNumber: '*********',
  phoneRegionCode: 'TW',
  roles: ['admin'],
};

// Mock API delay function
export const mockDelay = (ms: number = 500) =>
  new Promise((resolve) => setTimeout(resolve, ms));
