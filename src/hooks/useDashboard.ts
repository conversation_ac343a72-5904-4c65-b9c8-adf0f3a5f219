import { useQuery } from '@tanstack/react-query';
import { queryKeys } from '../constants/queryKeys';
import { fetchDashboardStats } from '../pages/Dashboard/apis/dashboardApis';

export const useDashboardStats = () => {
  return useQuery({
    queryKey: queryKeys.dashboard.stats,
    queryFn: fetchDashboardStats,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};
