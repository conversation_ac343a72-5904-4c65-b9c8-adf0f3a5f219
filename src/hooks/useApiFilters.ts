import { useState, useCallback, useMemo } from 'react';
import type { 
  <PERSON>pi<PERSON>ilt<PERSON>, 
  ApiSort, 
  ApiFiltersParams, 
  UseApiFiltersConfig,
  FilterOperator,
  SortDirection 
} from '@/types';
import { buildApiQueryString, isValidOperatorForType } from '@/utils/apiUtils';

/**
 * Generic hook for managing API filters and sorting
 * Provides methods to add, remove, and update filters and sorts
 * Returns formatted parameters ready for API calls
 */
export const useApiFilters = (config: UseApiFiltersConfig = {}) => {
  const {
    defaultFilters = [],
    defaultSorts = [],
    defaultPage = 1,
    defaultPageSize = 10,
    filterConfigs = [],
    sortConfigs = []
  } = config;

  // State management
  const [filters, setFilters] = useState<ApiFilter[]>(defaultFilters);
  const [sorts, setSorts] = useState<ApiSort[]>(defaultSorts);
  const [page, setPage] = useState<number>(defaultPage);
  const [pageSize, setPageSize] = useState<number>(defaultPageSize);

  // Filter management methods
  const addFilter = useCallback((filter: ApiFilter) => {
    // Validate operator for field type if config is provided
    const filterConfig = filterConfigs.find(config => config.key === filter.key);
    if (filterConfig && !isValidOperatorForType(filterConfig.type, filter.operator)) {
      console.warn(`Invalid operator ${filter.operator} for field type ${filterConfig.type}`);
      return;
    }

    setFilters(prev => {
      // Remove existing filter for the same key and operator combination
      const filtered = prev.filter(f => !(f.key === filter.key && f.operator === filter.operator));
      return [...filtered, filter];
    });
    
    // Reset to first page when adding filter
    setPage(1);
  }, [filterConfigs]);

  const removeFilter = useCallback((key: string, operator?: FilterOperator) => {
    setFilters(prev => {
      if (operator) {
        return prev.filter(f => !(f.key === key && f.operator === operator));
      }
      return prev.filter(f => f.key !== key);
    });
    
    // Reset to first page when removing filter
    setPage(1);
  }, []);

  const updateFilter = useCallback((key: string, operator: FilterOperator, value: string | number | boolean) => {
    setFilters(prev => {
      const index = prev.findIndex(f => f.key === key && f.operator === operator);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = { key, operator, value };
        return updated;
      }
      return prev;
    });
    
    // Reset to first page when updating filter
    setPage(1);
  }, []);

  const clearFilters = useCallback(() => {
    setFilters([]);
    setPage(1);
  }, []);

  // Sort management methods
  const addSort = useCallback((sort: ApiSort) => {
    setSorts(prev => {
      // Remove existing sort for the same key
      const filtered = prev.filter(s => s.key !== sort.key);
      return [...filtered, sort];
    });
  }, []);

  const removeSort = useCallback((key: string) => {
    setSorts(prev => prev.filter(s => s.key !== key));
  }, []);

  const updateSort = useCallback((key: string, direction: SortDirection) => {
    setSorts(prev => {
      const index = prev.findIndex(s => s.key === key);
      if (index >= 0) {
        const updated = [...prev];
        updated[index] = { key, direction };
        return updated;
      }
      return prev;
    });
  }, []);

  const toggleSort = useCallback((key: string) => {
    setSorts(prev => {
      const existing = prev.find(s => s.key === key);
      if (existing) {
        // Toggle direction or remove if already desc
        if (existing.direction === 'asc') {
          return prev.map(s => s.key === key ? { ...s, direction: 'desc' as const } : s);
        } else {
          return prev.filter(s => s.key !== key);
        }
      } else {
        // Add new sort with asc direction
        return [...prev, { key, direction: 'asc' as const }];
      }
    });
  }, []);

  const clearSorts = useCallback(() => {
    setSorts([]);
  }, []);

  // Pagination methods
  const updatePage = useCallback((newPage: number) => {
    setPage(newPage);
  }, []);

  const updatePageSize = useCallback((newPageSize: number) => {
    setPageSize(newPageSize);
    setPage(1); // Reset to first page when changing page size
  }, []);

  // Reset all filters, sorts, and pagination
  const resetAll = useCallback(() => {
    setFilters(defaultFilters);
    setSorts(defaultSorts);
    setPage(defaultPage);
    setPageSize(defaultPageSize);
  }, [defaultFilters, defaultSorts, defaultPage, defaultPageSize]);

  // Computed values
  const apiParams = useMemo((): ApiFiltersParams => ({
    filters,
    sorts,
    page,
    pageSize
  }), [filters, sorts, page, pageSize]);

  const queryString = useMemo(() => {
    debugger;
    return buildApiQueryString(apiParams);
  }, [apiParams]);

  const hasFilters = useMemo(() => filters.length > 0, [filters]);
  const hasSorts = useMemo(() => sorts.length > 0, [sorts]);
  const hasActiveFiltering = useMemo(() => hasFilters || hasSorts, [hasFilters, hasSorts]);

  // Helper methods for getting filter/sort info
  const getFilterValue = useCallback((key: string, operator: FilterOperator) => {
    const filter = filters.find(f => f.key === key && f.operator === operator);
    return filter?.value;
  }, [filters]);

  const getSortDirection = useCallback((key: string) => {
    const sort = sorts.find(s => s.key === key);
    return sort?.direction;
  }, [sorts]);

  const isFiltered = useCallback((key: string, operator?: FilterOperator) => {
    if (operator) {
      return filters.some(f => f.key === key && f.operator === operator);
    }
    return filters.some(f => f.key === key);
  }, [filters]);

  const isSorted = useCallback((key: string) => {
    return sorts.some(s => s.key === key);
  }, [sorts]);

  return {
    // State
    filters,
    sorts,
    page,
    pageSize,
    
    // Computed
    apiParams,
    queryString,
    hasFilters,
    hasSorts,
    hasActiveFiltering,
    
    // Filter methods
    addFilter,
    removeFilter,
    updateFilter,
    clearFilters,
    
    // Sort methods
    addSort,
    removeSort,
    updateSort,
    toggleSort,
    clearSorts,
    
    // Pagination methods
    updatePage,
    updatePageSize,
    
    // Utility methods
    resetAll,
    getFilterValue,
    getSortDirection,
    isFiltered,
    isSorted,
    
    // Config
    filterConfigs,
    sortConfigs
  };
};
