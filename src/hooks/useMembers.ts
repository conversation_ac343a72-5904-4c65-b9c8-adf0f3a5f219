import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { queryKeys } from '@/constants/queryKeys.ts';
import {
  fetchMembers,
  registerMember,
  fetchMemberById,
} from '@/pages/Members/apis/membersApis.ts';
import type { CreateMemberRequest } from '@/types';

export const useMembers = (params?: {
  page?: number;
  pageSize?: number;
  search?: string;
}) => {
  return useQuery({
    queryKey: queryKeys.members.list(params),
    queryFn: () => fetchMembers(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useMember = (id: string) => {
  return useQuery({
    queryKey: queryKeys.members.detail(id),
    queryFn: () => fetchMemberById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

export const useRegisterMember = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (memberData: CreateMemberRequest) => registerMember(memberData),
    onSuccess: (response) => {
      // Invalidate and refetch members list
      queryClient.invalidateQueries({ queryKey: queryKeys.members.all });

      // Show success message
      message.success(response.message || 'Member registered successfully');
    },
    onError: (error: Error) => {
      // Show error message
      message.error(error.message || 'Failed to register member');
    },
  });
};
