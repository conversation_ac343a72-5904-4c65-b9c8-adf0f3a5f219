import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { message } from 'antd';
import { queryKeys } from '@/constants/queryKeys.ts';
import {
  fetchOrders,
  fetchOrderById,
  updateOrderStatus,
  editOrder,
  type EditOrderRequest,
} from '@/pages/Orders/apis/ordersApis.ts';
import type { Order, ApiFiltersParams, FilterConfig, SortConfig } from '@/types';
import { useApiFilters } from './useApiFilters';

// Filter configurations for orders - Updated to match backend API fields (PascalCase)
const orderFilterConfigs: FilterConfig[] = [
  // Backend API supported fields (using PascalCase for server communication)
  { key: 'orderType', type: 'enum', operators: ['eq', 'ne'] },
  { key: 'orderStatus', type: 'enum', operators: ['eq', 'ne'] },
  { key: 'userId', type: 'uuid', operators: ['eq', 'ne'] },
  { key: 'counterId', type: 'string', operators: ['eq', 'ne', 'c', 'sw', 'ew'] },
  { key: 'quantity', type: 'number', operators: ['eq', 'ne', 'gt', 'gte', 'lt', 'lte'] },
  { key: 'staffId', type: 'uuid', operators: ['eq', 'ne'] },
  { key: 'pricePerUnit', type: 'number', operators: ['eq', 'ne', 'gt', 'gte', 'lt', 'lte'] },
  { key: 'totalPrice', type: 'number', operators: ['eq', 'ne', 'gt', 'gte', 'lt', 'lte'] },
  { key: 'currencyUnit', type: 'enum', operators: ['eq', 'ne'] },
  { key: 'updatedAt', type: 'date', operators: ['eq', 'ne', 'gt', 'gte', 'lt', 'lte'] },
  { key: 'createdAt', type: 'date', operators: ['eq', 'ne', 'gt', 'gte', 'lt', 'lte'] },
];

// Sort configurations for orders - Updated to match backend API fields (PascalCase)
const orderSortConfigs: SortConfig[] = [
  // Backend API supported fields (using PascalCase for server communication)
  { key: 'createdAt', label: 'Created Date' },
  { key: 'updatedAt', label: 'Updated Date' },
  { key: 'pricePerUnit', label: 'Price Per Unit' },
  { key: 'totalPrice', label: 'Total Price' },
  { key: 'quantity', label: 'Quantity' },
  { key: 'orderStatus', label: 'Order Status' },
  { key: 'orderType', label: 'Order Type' },
  { key: 'currencyUnit', label: 'Currency' },
  { key: 'counterId', label: 'Counter ID' },
];

export const useOrders = (params?: ApiFiltersParams) => {
  return useQuery({
    queryKey: queryKeys.orders.list(params),
    queryFn: () => fetchOrders(params),
    staleTime: 1 * 60 * 1000, // 1 minute
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook that combines orders data fetching with filtering capabilities
 * Provides a complete solution for orders management with dynamic filtering and sorting
 */
export const useOrdersWithFilters = () => {
  const filterHook = useApiFilters({
    defaultSorts: [{ key: 'createdAt', direction: 'desc' }],
    defaultPage: 1,
    defaultPageSize: 10,
    filterConfigs: orderFilterConfigs,
    sortConfigs: orderSortConfigs,
  });
  const ordersQuery = useOrders(filterHook.apiParams);

  return {
    // Data and loading states
    ...ordersQuery,

    // Filter and sort management
    ...filterHook,

    // Convenience methods for common order filters - Updated for new API
    filterByStatus: (status: string) => {
      filterHook.addFilter({ key: 'orderStatus', operator: 'eq', value: status.toUpperCase() });
    },

    filterByOrderType: (orderType: string) => {
      filterHook.addFilter({ key: 'orderType', operator: 'eq', value: orderType.toUpperCase() });
    },

    filterByUser: (userId: string) => {
      filterHook.addFilter({ key: 'userId', operator: 'eq', value: userId });
    },

    filterByCounter: (counterId: string) => {
      filterHook.addFilter({ key: 'counterId', operator: 'eq', value: counterId });
    },

    filterByStaff: (staffId: string) => {
      filterHook.addFilter({ key: 'staffId', operator: 'eq', value: staffId });
    },

    filterByPriceRange: (min: number, max: number) => {
      filterHook.addFilter({ key: 'pricePerUnit', operator: 'gte', value: min });
      filterHook.addFilter({ key: 'pricePerUnit', operator: 'lte', value: max });
    },



    filterByQuantityRange: (min: number, max: number) => {
      filterHook.addFilter({ key: 'quantity', operator: 'gte', value: min });
      filterHook.addFilter({ key: 'quantity', operator: 'lte', value: max });
    },

    filterByCurrency: (currencyUnit: string) => {
      filterHook.addFilter({ key: 'currencyUnit', operator: 'eq', value: currencyUnit.toUpperCase() });
    },

    filterByDateRange: (startDate: string, endDate: string) => {
      filterHook.addFilter({ key: 'createdAt', operator: 'gte', value: startDate });
      filterHook.addFilter({ key: 'createdAt', operator: 'lte', value: endDate });
    },

    filterByUpdatedDateRange: (startDate: string, endDate: string) => {
      filterHook.addFilter({ key: 'updatedAt', operator: 'gte', value: startDate });
      filterHook.addFilter({ key: 'updatedAt', operator: 'lte', value: endDate });
    },

    // Clear specific filter types
    clearStatusFilter: () => filterHook.removeFilter('orderStatus'),
    clearOrderTypeFilter: () => filterHook.removeFilter('orderType'),
    clearUserFilter: () => filterHook.removeFilter('userId'),
    clearCounterFilter: () => filterHook.removeFilter('counterId'),
    clearStaffFilter: () => filterHook.removeFilter('staffId'),
    clearPriceFilter: () => filterHook.removeFilter('pricePerUnit'),

    clearQuantityFilter: () => filterHook.removeFilter('quantity'),
    clearCurrencyFilter: () => filterHook.removeFilter('currencyUnit'),
    clearDateFilter: () => filterHook.removeFilter('createdAt'),
    clearUpdatedDateFilter: () => filterHook.removeFilter('updatedAt'),
  };
};

export const useOrder = (id: string) => {
  return useQuery({
    queryKey: queryKeys.orders.detail(id),
    queryFn: () => fetchOrderById(id),
    enabled: !!id,
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, status }: { orderId: number; status: string }) =>
      updateOrderStatus(orderId, status),
    onSuccess: (response) => {
      // Invalidate and refetch orders list
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.all });

      // Show success message
      message.success(response.message || 'Order status updated successfully');
    },
    onError: (error: Error) => {
      // Show error message
      message.error(error.message || 'Failed to update order status');
    },
  });
};

export const useEditOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderId, orderData }: { orderId: number; orderData: EditOrderRequest }) =>
      editOrder(orderId, orderData),
    onSuccess: (response) => {
      // Invalidate and refetch orders list
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.all });

      // Invalidate specific order query
      queryClient.invalidateQueries({ queryKey: queryKeys.orders.detail(response.data.orderId) });

      // Show success message
      message.success(response.message || 'Order updated successfully');
    },
    onError: (error: Error) => {
      // Show error message
      message.error(error.message || 'Failed to update order');
    },
  });
};
