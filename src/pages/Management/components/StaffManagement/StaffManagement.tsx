import { PlusOutlined } from '@ant-design/icons';
import { Button, Flex } from 'antd';
import { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import AddStaffModal from './AddStaffModal';
import useStaffColumns from './useStaffManagementColumns';
import { GalaxyTable } from '@/components';
import { usePagination } from '@/hooks';
import {
  useDeleteStaff,
  useStaffs,
  type StaffsProps,
} from '@/pages/Management/apis';
import type { TabKey } from '@/pages/Management/Management';
import { useNotifyStore } from '@/stores';

interface StaffManagementProps {
  activeTab: TabKey;
}

const StaffManagement = (props: StaffManagementProps) => {
  const { activeTab } = props || {};

  const [openAddStaffModal, setOpenAddStaffModal] = useState(false);
  const [sort, setSort] = useState<Array<string>>();

  const { t } = useTranslation('staffManagement');
  const { pushBSQ } = useNotifyStore();
  const { currentPage, setCurrentPage, pageSize, setPageSize } = usePagination(
    {},
  );
  const params: StaffsProps = useMemo(
    () => ({
      page: currentPage,
      pageSize,
      sort,
    }),
    [currentPage, pageSize, sort],
  );
  const {
    data: staffs,
    isPending: loadingStaffs,
    isError,
    refetch: refetchStaffs,
    isRefetching: refetchingStaffs,
  } = useStaffs({
    enabled: activeTab === 'staffs',
    params,
  });
  const { mutate: deleteStaff, isPending: deletingStaff } = useDeleteStaff({
    onSuccess: () => {
      pushBSQ([
        {
          title: import.meta.env.VITE_APP_TITLE || 'YF Pay Admin',
          des: t('deleteSuccessDesc'),
        },
      ]);
      refetchStaffs();
    },
  });
  const { columns } = useStaffColumns({
    handleDeleteStaff: (userId) => {
      deleteStaff({ userId });
    },
    deletingStaff,
  });

  const dataSource = useMemo(() => {
    if (!staffs || isError) return [];

    return staffs.items;
  }, [staffs, isError]);

  const handleOpenAddStaffModal = () => {
    setOpenAddStaffModal(true);
  };
  const handleCloseAddStaffModal = () => {
    setOpenAddStaffModal(false);
  };

  return (
    <>
      <Flex vertical gap={16}>
        <Flex justify='space-between'>
          <Flex> </Flex>
          <Button
            type='primary'
            icon={<PlusOutlined />}
            onClick={handleOpenAddStaffModal}
          >
            {t('addStaff')}
          </Button>
        </Flex>

        <GalaxyTable
          columns={columns}
          data={dataSource}
          pagination={{
            current: currentPage,
            pageSize: pageSize,
            total: staffs?.totalItems || 0,
            onChange: (page, pageSize) => {
              setCurrentPage(page);
              setPageSize(pageSize);
            },
          }}
          loading={loadingStaffs || refetchingStaffs}
          onSortChange={(newSort) => {
            setSort(newSort);
          }}
          rowKey='id'
        />
      </Flex>

      <AddStaffModal
        open={openAddStaffModal}
        onCancel={handleCloseAddStaffModal}
        refetchStaffs={refetchStaffs}
      />
    </>
  );
};

export default StaffManagement;
