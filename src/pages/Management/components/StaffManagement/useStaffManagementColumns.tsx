import { DeleteOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, <PERSON>confirm, type TableColumnType } from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import Txt from '@/components/Txt';
import type { Staff } from '@/pages/Management/apis';
import { dateFormator } from '@/utils';

interface UseProps {
  handleDeleteStaff: (userId: string) => void;
  deletingStaff: boolean;
}

const useStaffColumns = (props: UseProps) => {
  const { handleDeleteStaff, deletingStaff } = props || {};

  const { t } = useTranslation('useStaffManagementColumns');

  const columns: Array<TableColumnType<Staff>> = useMemo(
    () => [
      {
        key: 'userName',
        dataIndex: 'userName',
        title: <Txt>{t('userName')}</Txt>,
        sorter: {
          multiple: 3,
        },
        render: (_, { userName }) => {
          return <Txt>{userName}</Txt>;
        },
      },
      // {
      //   key: 'createdBy',
      //   title: <Txt>{t('createdBy')}</Txt>,
      //   render: (_, { createdBy }) => {
      //     return <Txt>{createdBy}</Txt>;
      //   },
      // },
      {
        key: 'createdAt',
        dataIndex: 'createdAt',
        title: <Txt>{t('createdAt')}</Txt>,
        sorter: {
          multiple: 2,
        },
        render: (_, { createdAt }) => {
          return (
            <Txt>
              {createdAt
                ? dayjs(createdAt).format(dateFormator.accurate)
                : '--'}
            </Txt>
          );
        },
      },
      {
        key: 'updatedAt',
        dataIndex: 'updatedAt',
        title: <Txt>{t('updatedAt')}</Txt>,
        sorter: {
          multiple: 1,
        },
        render: (_, { updatedAt }) => {
          return (
            <Txt>
              {updatedAt
                ? dayjs(updatedAt).format(dateFormator.accurate)
                : '--'}
            </Txt>
          );
        },
      },
      {
        key: 'actions',
        title: <Txt>{t('actions')}</Txt>,
        render: (_, { id }) => {
          return (
            <Popconfirm
              title={t('confirmDeleteTitle')}
              cancelText={t('no')}
              okText={t('yes')}
              onConfirm={() => {
                handleDeleteStaff(id);
              }}
            >
              <Button
                type='link'
                size='small'
                icon={<DeleteOutlined />}
                loading={deletingStaff}
                className='!text-red-400'
              >
                {t('delete')}
              </Button>
            </Popconfirm>
          );
        },
      },
    ],
    [t, handleDeleteStaff, deletingStaff],
  );

  return { columns };
};

export default useStaffColumns;
