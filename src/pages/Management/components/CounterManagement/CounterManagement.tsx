import { PlusOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import React, { useMemo, useState } from 'react';
import { useCounterDetails, useCounterList, useDelCounter } from '../../apis';
import type { TabKey } from '../../Management';
import AddCounterModal from './AddCounterModal';
import CounterDetailsModal from './CounterDetailsModal';
import useCounterColumns from './useCounterColumns';
import GalaxyTable from '@/components/WeAreTableGalaxy/GalaxyTable';

interface CounterManage {
  activeTab: TabKey;
}

const CounterManagement: React.FC<CounterManage> = (props) => {
  const { activeTab } = props || {};

  const [showAddCounterModal, setShowAddCounterModal] = useState(false);
  const [showCounterDModal, setShowCounterDModal] = useState(false);

  // Queries
  const {
    data: countList,
    isError: counterErr,
    isLoading: countLoading,
  } = useCounterList({
    params: {
      pageSize: 50,
      page: 1,
    },
    enabled: activeTab === 'counters',
  });

  // mutate handle
  const [pagination, setPagination] = useState({
    current: countList?.pageNumber,
    pageSize: countList?.pageSize,
  });
  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
  };

  const countDt = useMemo(() => {
    if (!countList || counterErr) return [];
    return countList.items;
  }, [countList, counterErr]);

  const { mutate: delCounter, isPending: delPen } = useDelCounter({});
  // mutation
  const {
    mutate: detailGet,
    data: counterDetail,
    isError: detailErr,
    isPending: pendingD,
  } = useCounterDetails({});

  const handleDetailGet = (counterId: string) => {
    detailGet({ counterId });
  };

  const dCounterData = useMemo(() => {
    if (!counterDetail || detailErr) return null;
    return counterDetail;
  }, [counterDetail, detailErr]);
  const handleDelCounter = (counterId: string) => {
    delCounter({ counterId });
  };

  const columns = useCounterColumns({
    delCounter: (counterId) => {
      handleDelCounter(counterId);
    },
    detailCounter: (counterId) => {
      handleDetailGet(counterId);
    },
    onViewD: () => setShowCounterDModal(true),
    pendingD,
    delPen,
  });

  return (
    <>
      <div className='space-y-4'>
        <div className='flex justify-end items-center'>
          <Button
            type='primary'
            icon={<PlusOutlined />}
            onClick={() => setShowAddCounterModal(true)}
            size='large'
            className='bg-blue-600 hover:bg-blue-700'
          >
            Add Counter
          </Button>
        </div>

        <GalaxyTable
          data={countDt}
          columns={columns}
          loading={countLoading}
          pagination={{
            current: pagination.current || 1,
            pageSize: pagination.pageSize || 10,
            total: countList?.totalPages || 0,
            onChange: handlePaginationChange,
          }}
          rowKey='counterId'
        />
      </div>

      <AddCounterModal
        open={showAddCounterModal}
        onClose={() => setShowAddCounterModal(false)}
      />
      {dCounterData && (
        <CounterDetailsModal
          dCounterData={dCounterData}
          open={showCounterDModal}
          onClose={() => setShowCounterDModal(false)}
        />
      )}
    </>
  );
};

export default CounterManagement;
