import { DeleteOutlined, EyeOutlined, MoreOutlined } from '@ant-design/icons';
import { But<PERSON>, Popconfirm, type TableColumnType } from 'antd';
import type { CounterOptions } from '../../apis';
import DropdownAlpha from '@/components/DropdownYF/DropdownAlpha';
import { useResponsive } from '@/hooks/useResponsive';
import { formatDateTime } from '@/utils/tableUtils';

type CounterRowOptions = CounterOptions;

type UseProps = {
  delCounter: (counterId: string) => void;
  detailCounter: (counterId: string) => void;
  onViewD: () => void;
  pendingD: boolean;
  delPen: boolean;
};

const useCounterColumns = (useProps: UseProps) => {
  const { isMobile } = useResponsive();
  const { delCounter, detailCounter, onViewD, pendingD, delPen } = useProps;

  const columns: TableColumnType<CounterRowOptions>[] = [
    {
      title: 'Counter Name',
      dataIndex: 'counterName',
      key: 'name',
      render: (storeName: string) => (
        <div>
          <div className='font-medium '>{storeName}</div>
        </div>
      ),
    },
    {
      title: 'Location',
      dataIndex: 'location',
      key: 'location',
      render: (location: string) => (
        <div>
          <div className='font-medium '>{location || 'No Location'}</div>
        </div>
      ),
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
      render: (description: string) => (
        <div>
          <div className='font-medium '>{description || 'No description'}</div>
        </div>
      ),
    },
    {
      title: 'Created At',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (_: CounterOptions, record: CounterOptions) => (
        <div>
          <div
            className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 mt-1`}
          >
            {isMobile
              ? `${formatDateTime(record.createdAt).slice(0, 6)} - ${formatDateTime(record.updatedAt).slice(0, 6)}`
              : `${formatDateTime(record.createdAt)} `}
          </div>
        </div>
      ),
      width: isMobile ? 120 : undefined,
    },
    {
      title: 'Updated At',
      dataIndex: 'updatedAt',
      key: 'updatedAt',
      render: (_: CounterOptions, record: CounterOptions) => (
        <div>
          <div
            className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 mt-1`}
          >
            {isMobile
              ? ` ${formatDateTime(record.updatedAt).slice(0, 6)}`
              : `${formatDateTime(record.updatedAt)} `}
          </div>
        </div>
      ),
      width: isMobile ? 120 : undefined,
    },
    {
      title: 'Actions',
      key: 'actions',
      render: (_: CounterOptions, record: CounterOptions) => {
        const dropItems = [
          {
            key: 'details',
            item: (
              <Button
                type='text'
                className='w-full text-left'
                icon={<EyeOutlined />}
                onClick={() => {
                  detailCounter(record.counterId);
                  onViewD();
                }}
                loading={pendingD}
              >
                Counter Details
              </Button>
            ),
          },
          {
            key: 'delete',
            item: (
              <Popconfirm
                title='Are you sure you want to delete this counter?'
                onConfirm={() => delCounter(record.counterId)}
                okText='Yes'
                cancelText='No'
                okButtonProps={{ loading: delPen }}
              >
                <Button
                  icon={<DeleteOutlined />}
                  danger
                  type='text'
                  className='w-full text-left'
                >
                  Delete Counter
                </Button>
              </Popconfirm>
            ),
          },
        ];

        return (
          <DropdownAlpha
            buttonProps={{
              type: 'default',
              size: isMobile ? 'small' : 'middle',
            }}
            icon={<MoreOutlined />}
            items={dropItems}
            noUnderLink
            gap={8}
            itemHeight='fit-content'
            pannelMaxHeight={400}
          />
        );
      },
      width: isMobile ? 80 : 100,
    },
  ];
  return columns;
};

export default useCounterColumns;
export type { CounterRowOptions };
