import {
  CalendarOutlined,
  EnvironmentOutlined,
  FileTextOutlined,
  StockOutlined,
  TableOutlined,
  TagOutlined,
  ToolOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  Button,
  Card,
  Col,
  Descriptions,
  Form,
  Input,
  Row,
  Typography,
} from 'antd';
import TextArea from 'antd/es/input/TextArea';
import React, { useState } from 'react';
import { ModalPageYF } from '@/components';
import { useResponsive } from '@/hooks/useResponsive';
import {
  useEditCounterDetails,
  type CounterOptions,
  type EditCounterDetailsProps,
} from '@/pages/Management/apis';
import { useNotifyStore } from '@/stores';
import { formatDateTime } from '@/utils/tableUtils';

interface ICounterDetailsModalProps {
  open: boolean;
  onClose: () => void;
  dCounterData: CounterOptions;
}

const CounterDetailsModal: React.FC<ICounterDetailsModalProps> = (props) => {
  const { open, onClose, dCounterData } = props || {};
  const { pushBEQ, pushBSQ } = useNotifyStore();
  const { isMobile } = useResponsive();
  const { Paragraph } = Typography;
  const [editingD, setEditingD] = useState(false);
  const [dEditCounterForm] = Form.useForm();
  // form to handle reset
  const resetClose = () => dEditCounterForm.resetFields();

  // mutation
  const { mutate: editCounter, isPending: dEditing } = useEditCounterDetails({
    onSuccess: () => {
      resetClose();
      onClose();
      pushBSQ([
        {
          title: 'YF Page Admin',
          des: `Counter With ID #${dCounterData.counterId} Edit Successfully`,
        },
      ]);
    },
  });

  const needEdit = () => {
    setEditingD(true);
    dEditCounterForm.setFieldsValue({
      counterName: dCounterData?.counterName,
      location: dCounterData?.location,
      description: dCounterData?.description,
    });
  };
  const handleEditD = (values: EditCounterDetailsProps) => {
    const edited =
      values.counterName !== dCounterData.counterName ||
      values.location !== dCounterData.location ||
      values.description !== dCounterData.description;
    if (!edited) {
      resetClose();
      onClose();
      pushBEQ([{ title: 'YF Page Admin', des: 'No changes detected' }]);
    }
    editCounter({
      counterId: dCounterData.counterId,
      counterName: values.counterName || '',
      location: values.location || '',
      description: values.description || '',
    });
  };

  return (
    <ModalPageYF
      width={800}
      title={
        <div className='flex items-center gap-2 text-lg font-semibold mb-3'>
          <StockOutlined className='!text-blue-600' />
          Counter Manage
        </div>
      }
      open={open}
      onCancel={() => {
        resetClose();
        onClose();
      }}
      footer={null}
    >
      {/* Counter Details*/}
      <Card
        style={{ marginBottom: '16px' }}
        title={
          <div className='flex items-center text-base gap-1'>
            <FileTextOutlined className='!text-blue-600' />
            Counter Details
          </div>
        }
      >
        <Descriptions
          colon={false}
          size={isMobile ? 'small' : 'middle'}
          column={isMobile ? 1 : 2}
          labelStyle={{ fontWeight: 500, color: '#555' }}
        >
          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <UserOutlined className=' !mr-1' />
                Counter Name:
              </div>
            }
          >
            <div className='font-semibold text-base'>
              {dCounterData?.counterName}
            </div>
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <EnvironmentOutlined className='!mr-1' />
                Location:
              </div>
            }
            className='flex items-center'
          >
            <span> {dCounterData?.location || 'No Location'} </span>
          </Descriptions.Item>

          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <CalendarOutlined className='!mr-1' />
                Create Time:
              </div>
            }
          >
            <span>{formatDateTime(dCounterData?.createdAt as string)}</span>
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <CalendarOutlined className='!mr-1' />
                <span> Update Time:</span>
              </div>
            }
          >
            <span>{formatDateTime(dCounterData?.updatedAt as string)}</span>
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <TagOutlined className='!mr-1' />
                Payment Status:
              </div>
            }
          >
            <span className='text-yellow-500'> Unavailable Now:</span>
          </Descriptions.Item>
          <Descriptions.Item
            label={
              <div className='!text-gray-400'>
                <ToolOutlined className='!mr-1' />
                Action:
              </div>
            }
            className='!text-gray-300 !mr-1 action-custom'
          >
            <Button type='text' className='!text-purple-500' onClick={needEdit}>
              Edit Da Details
            </Button>
          </Descriptions.Item>
        </Descriptions>

        {/* Notes */}
        <div className='mt-4'>
          <div className='flex items-center gap-2 mb-2 !text-gray-400 font-medium'>
            <FileTextOutlined className='!text-gray-300' />
            Notes
          </div>
          <Paragraph className='text-gray-600 ml-6 mb-0'>
            {dCounterData?.description || 'No Description'}
          </Paragraph>
        </div>
      </Card>

      {editingD && (
        <Card>
          <Form
            form={dEditCounterForm}
            onFinish={handleEditD}
            layout='vertical'
          >
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  name='counterName'
                  label='Counter Name'
                  rules={[
                    { required: true, message: 'Please Enter Counter Name' },
                  ]}
                >
                  <Input
                    autoComplete='off'
                    prefix={<TableOutlined />}
                    placeholder='Enter Counter Name'
                    size='large'
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name='location'
                  label='Location'
                  rules={[{ required: true, message: 'Please Enter Location' }]}
                >
                  <Input
                    autoComplete='off'
                    placeholder=' Enter Location'
                    size='large'
                  />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item name='description' label='Description (Optional)'>
              <TextArea
                placeholder='Enter some description'
                rows={2}
                size='large'
              />
            </Form.Item>

            <Button
              type='primary'
              htmlType='submit'
              size='large'
              className='bg-blue-600 hover:bg-blue-700 w-full'
              loading={dEditing}
            >
              Submit
            </Button>
          </Form>
        </Card>
      )}
      <div className='flex justify-end gap-3'>
        <Button
          className='mt-2'
          onClick={() => {
            resetClose();
            onClose();
          }}
          size='large'
        >
          Cancel
        </Button>
      </div>
    </ModalPageYF>
  );
};

export default CounterDetailsModal;
