import { apiClient } from '@/api/apiClient';
import { useMasterQuery, type UseMasterQueryProps } from '@/hooks/useApiMaster';

type CounterListProps = {
  search?: string;
  filter?: string;
  sort?: string;
  page?: number;
  pageSize?: number;
};
type CounterOptions = {
  counterId: string;
  counterName: string;
  location: string;
  description: string;
  createdAt: string;
  updatedAt: string;
};
type CounterListRes = {
  totalItems: number;
  totalPages: number;
  pageSize: number;
  pageNumber: number;
  items: Array<CounterOptions>;
};

type Other = unknown;

const useCounterList = (
  useProps: UseMasterQueryProps<Other, CounterListProps, CounterListRes>,
) => {
  const { params, ...config } = useProps;
  const testQuery = useMasterQuery<CounterListRes, CounterListProps>({
    ...config,
    queryKey: ['counter-list', ...Object.values(params || {})],
    qf: () => {
      const request = apiClient
        .get('/admin/counter', { params: { ...params } })
        .then(({ data }) => data);
      return request;
    },
  });
  return { ...testQuery };
};

export { useCounterList };
export type { CounterOptions, CounterListRes, CounterListProps };
