import qs from 'qs';
import { apiClient } from '@/api';
import { DEFAULT_STALE_TIME, queryKeys } from '@/constants';
import { useMasterQuery, type UseMasterQueryProps } from '@/hooks';
import type { YFRole } from '@/types';

type StaffsProps = {
  search?: string;
  filter?: Array<string>;
  sort?: Array<string>;
  page?: number;
  pageSize?: number;
};
type Staff = {
  id: string;
  userName: string;
  isNew: boolean;
  roles: Array<YFRole>;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
};
type StaffsRes = {
  totalItems: number;
  totalPages: number;
  pageSize: number;
  pageNumber: number;
  items: Array<Staff>;
};

type Other = unknown;

const useStaffs = (
  useProps: UseMasterQueryProps<Other, StaffsProps, StaffsRes>,
) => {
  const { params, ...config } = useProps;
  const query = useMasterQuery<StaffsRes, StaffsProps>({
    ...config,
    queryKey: queryKeys.management.staffs(params),
    qf: () => {
      const request = apiClient
        .get('/admin/staff', {
          params: { ...params },
          paramsSerializer: (params) => {
            return qs.stringify(params, {
              arrayFormat: 'repeat',
            });
          },
        })
        .then(({ data }) => data);
      return request;
    },
    refetchOnWindowFocus: true,
    staleTime: DEFAULT_STALE_TIME,
  });
  return { ...query };
};

export { useStaffs };
export type { Staff, StaffsProps };
