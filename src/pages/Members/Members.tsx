import { EyeOutlined, TeamOutlined } from '@ant-design/icons';
import { <PERSON><PERSON>, Avatar, Button, Card, Space, Tag, Typography } from 'antd';

import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  useUserDetails,
  type UserOptions,
  useUserList,
  type UserDTRes,
} from './apis';
import MemberDetailsModal from './Modals/MemberDetailsModal';
import GalaxyTableWrxdie from '@/components/WeAreTableGalaxy/GalayxyTableWxrdie';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import MemberForm from '@/pages/Members/components/MemberForm';
import { useUserStore } from '@/stores';

import type { Member } from '@/types';

import { formatDate } from '@/utils/tableUtils.ts';

// const { Search } = Input;
const { Title, Text } = Typography;

const Members: React.FC = () => {
  const { t } = useTranslation('members');
  const { isDark } = useUserStore();
  const { isMobile } = useResponsive();
  const [showForm, setShowForm] = useState(false);
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedMember, setSelectedMember] = useState<UserDTRes | null>(null);
  // const [searchTerm, setSearchTerm] = useState('');
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  const avatarSize = useResponsiveValue({
    xs: 32,
    sm: 36,
    md: 40,
    lg: 40,
    xl: 40,
    '2xl': 40,
  });

  // request User
  const { data: userGalaxy, isLoading, error } = useUserList({});
  const {
    mutate: userDiting,
    // isError: detailErr,
    isPending: pendingD,
  } = useUserDetails({
    onSuccess: (userDT) => {
      setSelectedMember(userDT);
      setShowDetailsModal(true);
    },
  });

  const handleUserDt = (userId: string) => {
    userDiting({ userId });
  };

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
  };

  // FE use later

  const handleCloseDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedMember(null);
  };

  const columns = [
    {
      title: 'Member Name',
      dataIndex: 'userName',
      key: 'userName',
      render: (userName: string) => (
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <Avatar
            className='bg-gradient-to-br from-primary to-primary-dark text-white font-medium flex-shrink-0'
            size={avatarSize}
          >
            {userName.charAt(0).toUpperCase()}
          </Avatar>
          <div className='min-w-0 flex-1'>
            <div
              className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}
            >
              <Text
                className={`font-medium ${isDark ? 'text-gray-100' : 'text-gray-900'} ${isMobile ? 'text-sm' : ''} truncate`}
              >
                {userName}
              </Text>
            </div>
          </div>
        </div>
      ),
      sorter: (a: Member, b: Member) =>
        String(a.name || '').localeCompare(String(b.name || ''), 'vi', {
          sensitivity: 'base',
        }),
      width: isMobile ? 200 : undefined,
    },
    {
      title: 'Email',
      dataIndex: 'email',
      key: 'email',
      render: (email: string) => (
        <div className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
          <div className='flex-1'>
            {email && !isMobile && (
              <Text
                className={`text-xs ${isDark ? 'text-gray-500' : 'text-gray-400'} block truncate`}
              >
                {email}
              </Text>
            )}
          </div>
        </div>
      ),
      sorter: (a: Member, b: Member) =>
        String(a.name || '').localeCompare(String(b.name || ''), 'vi', {
          sensitivity: 'base',
        }),
      width: isMobile ? 200 : undefined,
    },
    {
      title: t('passport'),
      dataIndex: 'passportNumber',
      key: 'passportNumber',
      render: (passport: string) => (
        <div
          className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'} ${
            isDark ? 'bg-gray-700 text-gray-100' : 'bg-gray-50 text-gray-800'
          } ${isMobile ? 'px-2 py-1' : 'px-3 py-1'} rounded-lg inline-block`}
        >
          {isMobile ? passport.slice(-6) : passport}
        </div>
      ),
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: 'Phone Number',
      dataIndex: 'phoneNumber',
      key: 'phoneNumber',
      render: (phoneNumber: string) => (
        <div
          className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'} ${
            isDark ? 'bg-gray-700 text-gray-100' : 'bg-gray-50 text-gray-800'
          } ${isMobile ? 'px-2 py-1' : 'px-3 py-1'} rounded-lg inline-block`}
        >
          {isMobile ? phoneNumber.slice(-6) : phoneNumber}
        </div>
      ),
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: 'Roles',
      dataIndex: 'roles',
      key: 'roles',
      render: (roles: string[]) => roles.join(', '),
    },
    {
      title: t('registrationDate'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (_: string, record: UserOptions) => {
        const [createdDate, createdTime] = formatDate(record.createdAt).split(
          ',',
        );
        const [updatedDate, updatedTime] = formatDate(record.updatedAt).split(
          ',',
        );

        return (
          <div>
            {/* Created At */}
            <Text
              className={`${isDark ? 'text-gray-100' : 'text-gray-900'} block ${isMobile ? 'text-xs' : ''}`}
            >
              {t('createdAt')}: {createdDate}
            </Text>
            {!isMobile && (
              <Text
                className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
              >
                {createdTime}
              </Text>
            )}

            {/* Updated At */}
            <Text
              className={`${isDark ? 'text-gray-100' : 'text-gray-900'} block ${isMobile ? 'text-xs' : ''} mt-1`}
            >
              {t('updatedAt')}: {updatedDate}
            </Text>
            {!isMobile && (
              <Text
                className={`text-sm ${isDark ? 'text-gray-400' : 'text-gray-500'}`}
              >
                {updatedTime}
              </Text>
            )}
          </div>
        );
      },
    },
    {
      title: 'NewUser',
      dataIndex: 'isNew',
      key: 'isNew',
      render: (isNew: boolean) => <Tag>{isNew ? 'USnew' : 'fkOld'}</Tag>,
      filters: [
        { text: t('active'), value: 'True' },
        { text: t('inactive'), value: 'False' },
      ],
      onFilter: (value: string, record: UserOptions) => record.isNew === value,
      width: isMobile ? 80 : undefined,
    },
    {
      title: 'Roles',
      dataIndex: 'roles',
      key: 'roles',
      render: (roles: string[]) => (
        <div className={`flex flex-wrap gap-1`}>
          {roles && roles.length > 0 ? (
            roles.map((role) => (
              <Tag
                key={role}
                color={isDark ? 'geekblue' : 'blue'}
                className={`${isMobile ? 'text-xs' : 'text-sm'}`}
              >
                {role}
              </Tag>
            ))
          ) : (
            <Tag color='default'>No Roles</Tag>
          )}
        </div>
      ),
      width: isMobile ? 120 : undefined,
    },
    {
      title: t('actions'),
      key: 'actions',
      width: isMobile ? 60 : 120,
      render: (_: UserOptions, record: UserOptions) => (
        <Space>
          <Button
            type='text'
            icon={<EyeOutlined />}
            className={`hover:${isDark ? 'bg-primary/20 text-primary' : 'bg-primary/10 text-primary'} rounded-lg`}
            onClick={() => handleUserDt(record.id)}
            size={isMobile ? 'small' : 'middle'}
            loading={pendingD}
          >
            {!isMobile && t('view')}
          </Button>
        </Space>
      ),
    },
  ];

  if (error) {
    return (
      <Alert
        message='Error'
        description='Failed to load members'
        type='error'
        showIcon
        className='rounded-lg'
      />
    );
  }

  const members = userGalaxy?.items || [];
  const total = userGalaxy?.totalItems || 0;

  return (
    <div
      className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6 lg:space-y-8'}`}
    >
      {/* Header Section */}
      <div
        className={`${
          isDark
            ? 'bg-zinc-800 border-zinc-700'
            : 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-100'
        } ${isMobile ? 'rounded-xl p-4' : 'rounded-2xl p-6 lg:p-8'} border `}
      >
        <div
          className={`flex ${isMobile ? 'flex-col gap-4' : 'items-center justify-between'}`}
        >
          <div className={`flex items-center ${isMobile ? 'gap-3' : 'gap-4'}`}>
            <div
              className={`${isMobile ? 'w-10 h-10' : 'w-12 h-12'} bg-gradient-to-br from-primary to-primary-dark rounded-xl flex items-center justify-center flex-shrink-0`}
            >
              <TeamOutlined
                className={`text-white ${isMobile ? 'text-lg' : 'text-xl'}`}
              />
            </div>
            <div className='min-w-0 flex-1'>
              <Title
                level={2}
                className={`${
                  isDark ? 'text-white' : 'text-gray-900'
                } mb-1 font-bold ${isMobile ? 'text-lg' : ''}`}
              >
                {t('title')}
              </Title>
              <Text
                className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-base'}`}
              >
                {t('description')}
              </Text>
            </div>
          </div>
        </div>
      </div>

      {/* Members Table */}
      <Card
        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card overflow-hidden`}
      >
        <GalaxyTableWrxdie
          data={members}
          columns={columns}
          loading={isLoading}
          customSort={true}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total,
            onChange: handlePaginationChange,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: !isMobile
              ? (total, range) =>
                  `${range[0]}-${range[1]} ${t('of')} ${total} ${t('items')}`
              : undefined,
          }}
          rowKey='id'
          scroll={isMobile ? { x: 800 } : undefined}
        />
      </Card>

      <MemberForm
        visible={showForm}
        onCancel={() => setShowForm(false)}
        onSuccess={() => {}}
      />

      <MemberDetailsModal
        visible={showDetailsModal}
        member={selectedMember}
        onClose={handleCloseDetailsModal}
        onEdit={(member) => {
          console.log('Edit member:', member);
        }}
      />
    </div>
  );
};

export default Members;
