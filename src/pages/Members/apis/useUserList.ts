import { apiClient } from '@/api/apiClient';
import { useMasterQuery, type UseMasterQueryProps } from '@/hooks/useApiMaster';

type UserListProps = {
  search?: string;
  filter?: string;
  sort?: string;
  page?: number;
  pageSize?: number;
};
type UserOptions = {
  id: string;
  phoneNumber: string;
  userName: string;
  email: string;
  isNew: boolean | string;
  passportNumber: string;
  roles: Array<string>;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
};
type UserListRes = {
  totalItems: number;
  totalPages: number;
  pageSize: number;
  pageNumber: number;
  items: Array<UserOptions>;
};

type Other = unknown;

const useUserList = (
  useProps: UseMasterQueryProps<Other, UserListProps, UserListRes>,
) => {
  const { params, ...config } = useProps;
  const testQuery = useMasterQuery<UserListRes, UserListProps>({
    ...config,
    queryKey: ['counter-list', ...Object.values(params || {})],
    qf: () => {
      const request = apiClient
        .get('/admin/user', { params: { ...params } })
        .then(({ data }) => data);
      return request;
    },
  });
  return { ...testQuery };
};

export { useUserList };
export type { UserOptions, UserListRes, UserListProps };
