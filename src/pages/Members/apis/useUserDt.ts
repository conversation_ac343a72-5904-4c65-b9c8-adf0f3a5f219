import { apiClient } from '@/api/apiClient';
import {
  useMasterMutation,
  type UseMasterMutationProps,
} from '@/hooks/useApiMaster';

type UserDTProps = {
  userId: string;
};

type UserDTRes = {
  id: string;
  phoneNumber: string;
  userName: string;
  email: string;
  isNew: boolean;
  roles: Array<string>;
  passport: UserPassport;
};

type UserPassport = {
  passportType: number;
  passportNumber: string;
  country: string;
  issuedBy: string;
  firstName: string;
  lastName: string;
  gender: string;
  dateOfBirth: string;
  nationality: string;
  placeOfBirth: string;
  issueDate: string;
  expirationDate: string;
  remarks: string;
};

type Other = unknown;

const useUserDetails = (
  useProps: UseMasterMutationProps<UserDTRes, UserDTProps, Other>,
) => {
  const { ...config } = useProps;

  const testMutation = useMasterMutation<UserDTRes, UserDTProps>({
    ...config,
    mutationFn: (props) => {
      const { userId } = props;
      const request = apiClient
        .get(`/admin/user/${userId}/detail`)
        .then(({ data }) => data);
      return request;
    },
  });

  const { mutate } = testMutation;

  return { ...testMutation, mutate };
};

export { useUserDetails };
export type { UserDTProps, UserDTRes, UserPassport };
