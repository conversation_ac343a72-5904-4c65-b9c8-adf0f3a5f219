import {
  CloseOutlined,
  HomeOutlined,
  IdcardOutlined,
  MailOutlined,
  PhoneOutlined,
  ShoppingCartOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { Avatar, Card, Descriptions, Modal, Typography } from 'antd';

import React from 'react';
import { useTranslation } from 'react-i18next';
import type { UserDTRes } from '../apis';
import { OrderStatusTag } from '@/components';
import GalaxyTable from '@/components/WeAreTableGalaxy/GalaxyTable';
import { useOrders } from '@/hooks/useOrders.ts';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useUserStore } from '@/stores';
import type { Member, Order } from '@/types';
import {
  formatCurrency,
  formatDate,
  formatDateTime,
} from '@/utils/tableUtils.ts';

const { Title, Text, Paragraph } = Typography;

interface MemberDetailsModalProps {
  visible: boolean;
  member: UserDTRes | null;
  onClose: () => void;
  onEdit?: (member: Member) => void;
}

const MemberDetailsModal: React.FC<MemberDetailsModalProps> = (props) => {
  const { t } = useTranslation('memberDetailsModal');
  const { isMobile } = useResponsive();

  // Responsive values
  const modalWidth = useResponsiveValue({
    xs: '95%',
    sm: '90%',
    md: '800px',
    lg: '900px',
    xl: '900px',
    '2xl': '900px',
  });

  const avatarSize = useResponsiveValue({
    xs: 48,
    sm: 56,
    md: 64,
    lg: 64,
    xl: 64,
    '2xl': 64,
  });

  const descriptionColumns = useResponsiveValue({
    xs: 1,
    sm: 1,
    md: 2,
    lg: 2,
    xl: 2,
    '2xl': 2,
  });

  const { visible, member, onClose } = props || {};
  const { isDark } = useUserStore();

  // Fetch member's orders
  const { data: ordersResponse, isLoading: ordersLoading } = useOrders({
    memberId: member?.id,
    page: 1,
    pageSize: 10,
  });

  const memberOrders = ordersResponse?.data?.data || [];

  if (!member) return null;

  // const getStatusColor = (status: string) => {
  //   return status === 'active' ? 'green' : 'red';
  // };

  const orderColumns = [
    {
      title: t('orderId'),
      dataIndex: 'id',
      key: 'id',
      width: isMobile ? 100 : 120,
      render: (id: string) => (
        <span className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'}`}>
          {isMobile ? id.slice(-6) : id}
        </span>
      ),
    },
    {
      title: t('amount'),
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => (
        <span
          className={`font-medium text-green-600 ${isMobile ? 'text-sm' : ''}`}
        >
          {formatCurrency(amount)}
        </span>
      ),
      width: isMobile ? 100 : undefined,
    },
    {
      title: t('status'),
      dataIndex: 'status',
      key: 'status',
      render: (status: Order['status']) => {
        return <OrderStatusTag status={status} />;
      },
      width: isMobile ? 80 : undefined,
    },
    {
      title: t('date'),
      dataIndex: 'date',
      key: 'date',
      render: (date: string) => (
        <span className={`${isMobile ? 'text-xs' : 'text-sm'}`}>
          {isMobile ? formatDate(date) : formatDateTime(date)}
        </span>
      ),
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
  ];

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={modalWidth}
      className={isMobile ? 'top-4' : 'top-8'}
      closeIcon={
        <CloseOutlined className='text-gray-400 hover:text-gray-600' />
      }
      styles={{
        body: { padding: isMobile ? '0px' : '24px' },
      }}
    >
      <div>
        {/* Header */}
        <div
          className={`flex ${isMobile ? 'flex-col gap-4' : 'items-center justify-between'} mb-6`}
        >
          <div className={`flex items-center ${isMobile ? 'gap-3' : 'gap-4'}`}>
            <Avatar
              size={avatarSize}
              className={`bg-gradient-to-br from-blue-500 to-purple-600 text-white font-bold ${isMobile ? 'text-lg' : 'text-xl'} flex-shrink-0`}
            >
              {member.userName.charAt(0).toUpperCase()}
            </Avatar>
            <div className='min-w-0 flex-1'>
              <div
                className={`flex ${isMobile ? 'flex-col gap-1' : 'items-center gap-3'}`}
              >
                <Title
                  level={3}
                  className={`text-gray-900 mb-0 font-bold ${isMobile ? 'text-lg' : ''}`}
                >
                  {member.userName}
                </Title>
              </div>
              <div
                className={`flex ${isMobile ? 'flex-col gap-1' : 'items-center gap-2'} mt-1`}
              >
                <Text className={`text-gray-600 ${isMobile ? 'text-sm' : ''}`}>
                  {t('memberId')}: {member.id}
                </Text>
              </div>
            </div>
          </div>
        </div>

        {/* Member Details */}
        <Card
          className='mb-6'
          title={
            <span
              className={`flex items-center gap-2 ${isMobile ? 'text-sm' : ''}`}
            >
              <UserOutlined className='text-blue-600' />
              {t('personalInfo')}
            </span>
          }
        >
          <Descriptions
            column={descriptionColumns}
            size={isMobile ? 'small' : 'middle'}
          >
            <Descriptions.Item
              label={
                <span className='flex items-center gap-2'>
                  <IdcardOutlined className='text-gray-500' />
                  {t('passport')}
                </span>
              }
            >
              <span
                className={`${isDark ? 'bg-slate-600' : 'bg-slate-100'} font-mono px-2 py-1 rounded`}
              >
                {member.email}
              </span>
            </Descriptions.Item>

            {member.email && (
              <Descriptions.Item
                label={
                  <span className='flex items-center gap-2'>
                    <MailOutlined className='text-gray-500' />
                    {t('email')}
                  </span>
                }
              >
                <a
                  href={`mailto:${member.email}`}
                  className='text-blue-600 hover:text-blue-800'
                >
                  {member.email}
                </a>
              </Descriptions.Item>
            )}

            {member.phoneNumber && (
              <Descriptions.Item
                label={
                  <span className='flex items-center gap-2'>
                    <PhoneOutlined className='text-gray-500' />
                    {t('phone')}
                  </span>
                }
              >
                <a
                  href={`tel:${member.phoneNumber}`}
                  className='text-blue-600 hover:text-blue-800'
                >
                  {member.phoneNumber}
                </a>
              </Descriptions.Item>
            )}
          </Descriptions>

          {member.email && (
            <div className='mt-4'>
              <div className='flex items-center gap-2 mb-2'>
                <HomeOutlined className='text-gray-500' />
                <Text className='font-medium text-gray-700'>
                  {t('address')}
                </Text>
              </div>
              <Paragraph className='text-gray-600 ml-6 mb-0'>
                {member.email}
              </Paragraph>
            </div>
          )}
        </Card>

        {/* Recent Orders */}
        <Card
          title={
            <span
              className={`flex items-center gap-2 ${isMobile ? 'text-sm' : ''}`}
            >
              <ShoppingCartOutlined className='text-blue-600' />
              {t('recentOrders')}
            </span>
          }
          extra={
            !isMobile && (
              <Text className='text-gray-500'>
                {t('showing')} {memberOrders.length} {t('orders')}
              </Text>
            )
          }
        >
          {memberOrders.length > 0 ? (
            <GalaxyTable
              data={memberOrders}
              columns={orderColumns}
              loading={ordersLoading}
              pagination={undefined}
              rowKey='id'
              size={isMobile ? 'small' : 'small'}
              scroll={isMobile ? { x: 400 } : undefined}
            />
          ) : (
            <div className={`text-center ${isMobile ? 'py-6' : 'py-8'}`}>
              <ShoppingCartOutlined
                className={`${isMobile ? 'text-3xl' : 'text-4xl'} text-gray-300 mb-4`}
              />
              <Text className={`text-gray-500 ${isMobile ? 'text-sm' : ''}`}>
                {t('noOrders')}
              </Text>
            </div>
          )}
        </Card>
      </div>
    </Modal>
  );
};

export default MemberDetailsModal;
