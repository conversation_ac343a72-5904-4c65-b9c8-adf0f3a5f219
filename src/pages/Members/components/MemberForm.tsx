import {
  UserOutlined,
  IdcardOutlined,
  UserAddOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { Form, Input, Button, Modal, Typography, Divider } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useRegisterMember } from '@/hooks/useMembers.ts';
import type { CreateMemberRequest } from '@/types';

const { Title, Text } = Typography;

interface MemberFormProps {
  visible: boolean;
  onCancel: () => void;
  onSuccess?: () => void;
}

const MemberForm: React.FC<MemberFormProps> = ({
  visible,
  onCancel,
  onSuccess,
}) => {
  const { t } = useTranslation('memberForm');
  const [form] = Form.useForm();
  const registerMemberMutation = useRegisterMember();

  const handleSubmit = async (values: CreateMemberRequest) => {
    try {
      await registerMemberMutation.mutateAsync(values);
      form.resetFields();
      onSuccess?.();
      onCancel();
    } catch (error) {
      console.log(error);
      // Error is handled by the mutation
    }
  };

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  return (
    <Modal
      open={visible}
      onCancel={handleCancel}
      footer={null}
      width={600}
      centered
      className='rounded-2xl overflow-hidden'
      closeIcon={
        <CloseOutlined className='text-gray-400 hover:text-gray-600' />
      }
    >
      <div className='p-6'>
        {/* Header */}
        <div className='text-center mb-8'>
          <div className='w-16 h-16 bg-gradient-to-br from-primary to-primary-dark rounded-2xl flex items-center justify-center mx-auto mb-4'>
            <UserAddOutlined className='text-white text-2xl' />
          </div>
          <Title level={3} className='text-gray-900 mb-2 font-bold'>
            {t('registerMember')}
          </Title>
          <Text className='text-gray-600'>{t('description')}</Text>
        </div>

        <Divider className='my-6' />

        {/* Form */}
        <Form
          form={form}
          layout='vertical'
          onFinish={handleSubmit}
          className='space-y-6'
        >
          <Form.Item
            name='name'
            label={
              <Text className='font-medium text-gray-700'>
                {t('nameLabel')}
              </Text>
            }
            rules={[
              { required: true, message: t('nameError') },
              { min: 2, message: t('nameLengthError') },
            ]}
          >
            <Input
              prefix={
                <div className='w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center mr-2'>
                  <UserOutlined className='text-primary' />
                </div>
              }
              placeholder={t('namePlaceholder')}
              size='large'
              className='rounded-lg border-gray-200 hover:border-primary focus:border-primary'
              autoComplete='false'
            />
          </Form.Item>

          <Form.Item
            name='passport'
            label={
              <Text className='font-medium text-gray-700'>
                {t('passportLabel')}
              </Text>
            }
            rules={[
              { required: true, message: t('passportError') },
              { min: 5, message: t('passportLengthError') },
            ]}
          >
            <Input
              prefix={
                <div className='w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center mr-2'>
                  <IdcardOutlined className='text-primary' />
                </div>
              }
              placeholder={t('passportPlaceholder')}
              size='large'
              className='rounded-lg border-gray-200 hover:border-primary focus:border-primary font-mono'
              autoComplete='false'
            />
          </Form.Item>

          <Divider className='my-6' />

          {/* Actions */}
          <div className='flex justify-end gap-3 pt-4'>
            <Button
              onClick={handleCancel}
              size='large'
              className='rounded-lg px-6 font-medium border-gray-200 hover:border-gray-300'
            >
              {t('cancel')}
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              loading={registerMemberMutation.isPending}
              size='large'
              className='rounded-lg px-8 font-medium shadow-lg hover:shadow-xl transition-all'
              icon={<UserAddOutlined />}
            >
              {registerMemberMutation.isPending
                ? t('registering')
                : t('register')}
            </Button>
          </div>
        </Form>
      </div>
    </Modal>
  );
};

export default MemberForm;
