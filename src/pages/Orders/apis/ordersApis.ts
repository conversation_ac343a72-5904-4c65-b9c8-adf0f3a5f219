import { mockOrders, mockDelay } from '@/api/mockData.ts';
import { apiClient } from '@/api/apiClient';
import type {
  Order,
  ApiResponse,
  PaginatedResponse,
  BackendPaginatedResponse,
  ApiFiltersParams,
  ApiFilter
} from '@/types';
import { buildApiQueryString } from '@/utils/apiUtils';

/**
 * Check if we should use mock data based on environment
 */
const shouldUseMockData = (): boolean => {
  return import.meta.env.VITE_MOCK_API === 'true';
};

/**
 * Apply a single filter to the orders array (for mock data)
 */
const applyFilter = (orders: Order[], filter: ApiFilter): Order[] => {
  const { key, operator, value } = filter;

  return orders.filter(order => {
    let orderValue = (order as any)[key];

    // Handle computed fields
    if (key === 'totalPrice') {
      orderValue = order.quantity * order.pricePerUnit;
    }

    if (orderValue === undefined || orderValue === null) {
      return false;
    }

    switch (operator) {
      case 'eq':
        return orderValue === value;
      case 'ne':
        return orderValue !== value;
      case 'c':
        return String(orderValue).toLowerCase().includes(String(value).toLowerCase());
      case 'sw':
        return String(orderValue).toLowerCase().startsWith(String(value).toLowerCase());
      case 'ew':
        return String(orderValue).toLowerCase().endsWith(String(value).toLowerCase());
      case 'gt':
        return Number(orderValue) > Number(value);
      case 'gte':
        return Number(orderValue) >= Number(value);
      case 'lt':
        return Number(orderValue) < Number(value);
      case 'lte':
        return Number(orderValue) <= Number(value);
      default:
        return true;
    }
  });
};

/**
 * Apply sorting to the orders array (for mock data)
 */
const applySorting = (orders: Order[], sorts: any[]): Order[] => {
  if (!sorts || sorts.length === 0) {
    // Default sort by date (newest first)
    return [...orders].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime());
  }

  return [...orders].sort((a, b) => {
    for (const sort of sorts) {
      const { key, direction } = sort;
      let aValue = (a as any)[key];
      let bValue = (b as any)[key];

      // Handle computed fields
      if (key === 'totalPrice') {
        aValue = a.quantity * a.pricePerUnit;
        bValue = b.quantity * b.pricePerUnit;
      }

      let comparison = 0;

      if (aValue < bValue) {
        comparison = -1;
      } else if (aValue > bValue) {
        comparison = 1;
      }

      if (comparison !== 0) {
        return direction === 'desc' ? -comparison : comparison;
      }
    }
    return 0;
  });
};

/**
 * Real API call to fetch orders
 */
const fetchOrdersFromAPI = async (params?: ApiFiltersParams): Promise<ApiResponse<PaginatedResponse<Order>>> => {
  try {
    const queryString = buildApiQueryString(params || {});
    const url = queryString ? `/admin/order?${queryString}` : '/admin/order';

    console.log('Fetching orders from API:', url);

    const response = await apiClient.get<BackendPaginatedResponse<Order>>(url);

    // Transform backend response to frontend format
    const transformedData: PaginatedResponse<Order> = {
      data: response.data.items,
      total: response.data.totalItems,
      page: response.data.pageNumber,
      pageSize: response.data.pageSize,
    };

    return {
      data: transformedData,
      success: true,
      message: 'Orders fetched successfully from API',
    };
  } catch (error) {
    console.error('Error fetching orders from API:', error);
    throw error;
  }
};

/**
 * Mock API function for development/testing
 */
const fetchOrdersFromMock = async (params?: ApiFiltersParams): Promise<ApiResponse<PaginatedResponse<Order>>> => {
  await mockDelay();

  const { page = 1, pageSize = 10, filters = [], sorts = [] } = params || {};

  // Start with all mock orders
  let filteredOrders = [...mockOrders];

  // Apply filters
  if (filters && filters.length > 0) {
    filters.forEach(filter => {
      filteredOrders = applyFilter(filteredOrders, filter);
    });
  }

  // Apply sorting
  filteredOrders = applySorting(filteredOrders, sorts);

  // Paginate results
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedOrders = filteredOrders.slice(startIndex, endIndex);

  const queryString = buildApiQueryString(params || {});
  console.log('Orders Mock API called with query (PascalCase parameters):', queryString);

  return {
    data: {
      data: paginatedOrders,
      total: filteredOrders.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Orders fetched successfully from mock',
  };
};

/**
 * Main function to fetch orders - switches between mock and real API
 */
export const fetchOrders = async (params?: ApiFiltersParams): Promise<ApiResponse<PaginatedResponse<Order>>> => {
  if (shouldUseMockData()) {
    return fetchOrdersFromMock(params);
  } else {
    return fetchOrdersFromAPI(params);
  }
};

/**
 * Fetch single order by ID from real API
 */
const fetchOrderByIdFromAPI = async (id: string): Promise<ApiResponse<Order>> => {
  try {
    const response = await apiClient.get<Order>(`/api/admin/order/${id}`);

    return {
      data: response.data,
      success: true,
      message: 'Order fetched successfully from API',
    };
  } catch (error) {
    console.error('Error fetching order from API:', error);
    throw error;
  }
};

/**
 * Fetch single order by ID from mock data
 */
const fetchOrderByIdFromMock = async (id: string): Promise<ApiResponse<Order>> => {
  await mockDelay();

  const order = mockOrders.find((o) => o.orderCode === id);

  if (!order) {
    throw new Error('Order not found');
  }

  return {
    data: order,
    success: true,
    message: 'Order fetched successfully from mock',
  };
};

/**
 * Main function to fetch order by ID - switches between mock and real API
 */
export const fetchOrderById = async (id: string): Promise<ApiResponse<Order>> => {
  if (shouldUseMockData()) {
    return fetchOrderByIdFromMock(id);
  } else {
    return fetchOrderByIdFromAPI(id);
  }
};

/**
 * Update order status via real API
 */
const updateOrderStatusFromAPI = async (
  id: string,
  status: string,
): Promise<ApiResponse<Order>> => {
  try {
    const response = await apiClient.put<Order>(`/api/admin/order/${id}/status`, {
      orderStatus: status.toUpperCase()
    });

    return {
      data: response.data,
      success: true,
      message: 'Order status updated successfully via API',
    };
  } catch (error) {
    console.error('Error updating order status via API:', error);
    throw error;
  }
};

/**
 * Update order status in mock data
 */
const updateOrderStatusFromMock = async (
  id: string,
  status: string,
): Promise<ApiResponse<Order>> => {
  await mockDelay();

  const orderIndex = mockOrders.findIndex((o) => o.orderCode === id);

  if (orderIndex === -1) {
    throw new Error('Order not found');
  }

  // Update order status in mock data
  const updatedOrder = {
    ...mockOrders[orderIndex],
    orderStatus: status.toUpperCase() as any,
    updatedAt: new Date().toISOString(),
  };

  mockOrders[orderIndex] = updatedOrder;

  return {
    data: updatedOrder,
    success: true,
    message: 'Order status updated successfully in mock',
  };
};

/**
 * Main function to update order status - switches between mock and real API
 */
export const updateOrderStatus = async (
  id: string,
  status: string,
): Promise<ApiResponse<Order>> => {
  if (shouldUseMockData()) {
    return updateOrderStatusFromMock(id, status);
  } else {
    return updateOrderStatusFromAPI(id, status);
  }
};
