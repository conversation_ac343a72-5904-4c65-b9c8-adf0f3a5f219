import { mockOrders, mockDelay } from '@/api/mockData.ts';
import { apiClient } from '@/api/apiClient';
import type {
  Order,
  ApiResponse,
  PaginatedResponse,
  BackendPaginatedResponse,
  ApiFiltersParams,
  ApiFilter
} from '@/types';
import { buildApiQueryString } from '@/utils/apiUtils';

/**
 * Check if we should use mock data based on environment
 */
const shouldUseMockData = (): boolean => {
  return import.meta.env.VITE_MOCK_API === 'true';
};

/**
 * Apply a single filter to the orders array (for mock data)
 */
const applyFilter = (orders: Order[], filter: ApiFilter): Order[] => {
  const { key, operator, value } = filter;

  return orders.filter(order => {
    let orderValue = (order as any)[key];

    // Handle computed fields
    if (key === 'totalPrice') {
      orderValue = order.quantity * order.pricePerUnit;
    }

    if (orderValue === undefined || orderValue === null) {
      return false;
    }

    switch (operator) {
      case 'eq':
        return orderValue === value;
      case 'ne':
        return orderValue !== value;
      case 'c':
        return String(orderValue).toLowerCase().includes(String(value).toLowerCase());
      case 'sw':
        return String(orderValue).toLowerCase().startsWith(String(value).toLowerCase());
      case 'ew':
        return String(orderValue).toLowerCase().endsWith(String(value).toLowerCase());
      case 'gt':
        return Number(orderValue) > Number(value);
      case 'gte':
        return Number(orderValue) >= Number(value);
      case 'lt':
        return Number(orderValue) < Number(value);
      case 'lte':
        return Number(orderValue) <= Number(value);
      default:
        return true;
    }
  });
};

/**
 * Apply sorting to the orders array (for mock data)
 */
const applyMockSorting = (orders: LegacyOrder[], sorts: any[]): LegacyOrder[] => {
  if (!sorts || sorts.length === 0) {
    // Default sort by date (newest first)
    return [...orders].sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
  }

  return [...orders].sort((a, b) => {
    for (const sort of sorts) {
      const { key, direction } = sort;
      const aValue = (a as any)[key];
      const bValue = (b as any)[key];

      let comparison = 0;

      if (aValue < bValue) {
        comparison = -1;
      } else if (aValue > bValue) {
        comparison = 1;
      }

      if (comparison !== 0) {
        return direction === 'desc' ? -comparison : comparison;
      }
    }
    return 0;
  });
};

/**
 * Real API call to fetch orders
 */
const fetchOrdersFromAPI = async (params?: ApiFiltersParams): Promise<ApiResponse<PaginatedResponse<OrderWithComputed>>> => {
  try {
    const queryString = buildApiQueryString(params || {});
    const url = queryString ? `/admin/order?${queryString}` : '/admin/order';

    console.log('Fetching orders from API:', url);

    const response = await apiClient.get<BackendPaginatedResponse<Order>>(url);
    const transformedData = transformOrdersResponse(response.data);

    return {
      data: transformedData,
      success: true,
      message: 'Orders fetched successfully from API',
    };
  } catch (error) {
    console.error('Error fetching orders from API:', error);
    throw error;
  }
};

/**
 * Mock API function for development/testing
 */
const fetchOrdersFromMock = async (params?: ApiFiltersParams): Promise<ApiResponse<PaginatedResponse<OrderWithComputed>>> => {
  await mockDelay();

  const { page = 1, pageSize = 10, filters = [], sorts = [] } = params || {};

  // Start with all mock orders (cast to LegacyOrder for compatibility)
  let filteredOrders = [...mockOrders] as LegacyOrder[];

  // Apply filters
  if (filters && filters.length > 0) {
    filters.forEach(filter => {
      filteredOrders = applyMockFilter(filteredOrders, filter);
    });
  }

  // Apply sorting
  filteredOrders = applyMockSorting(filteredOrders, sorts);

  // Transform to new format and paginate
  const transformedOrders = filteredOrders.map(order => {
    // Convert legacy order to new format for consistency
    const newOrder: Order = {
      orderId: parseInt(order.id) || 0,
      orderCode: order.id,
      orderType: order.type,
      userId: order.memberId,
      counterId: '0001',
      staffId: order.createdBy || '',
      quantity: 1,
      pricePerUnit: order.amount,
      currencyUnit: 'USD' as any,
      orderStatus: order.status.toUpperCase() as any,
      notes: order.notes || '',
      metadata: null,
      createdAt: order.date,
      updatedAt: order.updatedAt || order.date,
    };
    return transformBackendOrder(newOrder);
  });

  // Paginate results
  const startIndex = (page - 1) * pageSize;
  const endIndex = startIndex + pageSize;
  const paginatedOrders = transformedOrders.slice(startIndex, endIndex);

  const queryString = buildApiQueryString(params || {});
  console.log('Orders Mock API called with query:', queryString);

  return {
    data: {
      data: paginatedOrders,
      total: transformedOrders.length,
      page,
      pageSize,
    },
    success: true,
    message: 'Orders fetched successfully from mock',
  };
};

/**
 * Main function to fetch orders - switches between mock and real API
 */
export const fetchOrders = async (params?: ApiFiltersParams): Promise<ApiResponse<PaginatedResponse<OrderWithComputed>>> => {
  if (shouldUseMockData()) {
    return fetchOrdersFromMock(params);
  } else {
    return fetchOrdersFromAPI(params);
  }
};

/**
 * Fetch single order by ID from real API
 */
const fetchOrderByIdFromAPI = async (id: string): Promise<ApiResponse<OrderWithComputed>> => {
  try {
    const response = await apiClient.get<Order>(`/api/admin/order/${id}`);
    const transformedOrder = transformBackendOrder(response.data);

    return {
      data: transformedOrder,
      success: true,
      message: 'Order fetched successfully from API',
    };
  } catch (error) {
    console.error('Error fetching order from API:', error);
    throw error;
  }
};

/**
 * Fetch single order by ID from mock data
 */
const fetchOrderByIdFromMock = async (id: string): Promise<ApiResponse<OrderWithComputed>> => {
  await mockDelay();

  const order = mockOrders.find((o) => o.id === id) as LegacyOrder;

  if (!order) {
    throw new Error('Order not found');
  }

  // Transform legacy order to new format
  const newOrder: Order = {
    orderId: parseInt(order.id) || 0,
    orderCode: order.id,
    orderType: order.type,
    userId: order.memberId,
    counterId: '0001',
    staffId: order.createdBy || '',
    quantity: 1,
    pricePerUnit: order.amount,
    currencyUnit: 'USD' as any,
    orderStatus: order.status.toUpperCase() as any,
    notes: order.notes || '',
    metadata: null,
    createdAt: order.date,
    updatedAt: order.updatedAt || order.date,
  };

  const transformedOrder = transformBackendOrder(newOrder);

  return {
    data: transformedOrder,
    success: true,
    message: 'Order fetched successfully from mock',
  };
};

/**
 * Main function to fetch order by ID - switches between mock and real API
 */
export const fetchOrderById = async (id: string): Promise<ApiResponse<OrderWithComputed>> => {
  if (shouldUseMockData()) {
    return fetchOrderByIdFromMock(id);
  } else {
    return fetchOrderByIdFromAPI(id);
  }
};

/**
 * Update order status via real API
 */
const updateOrderStatusFromAPI = async (
  id: string,
  status: string,
): Promise<ApiResponse<OrderWithComputed>> => {
  try {
    const response = await apiClient.put<Order>(`/api/admin/order/${id}/status`, {
      orderStatus: status.toUpperCase()
    });
    const transformedOrder = transformBackendOrder(response.data);

    return {
      data: transformedOrder,
      success: true,
      message: 'Order status updated successfully via API',
    };
  } catch (error) {
    console.error('Error updating order status via API:', error);
    throw error;
  }
};

/**
 * Update order status in mock data
 */
const updateOrderStatusFromMock = async (
  id: string,
  status: string,
): Promise<ApiResponse<OrderWithComputed>> => {
  await mockDelay();

  const orderIndex = mockOrders.findIndex((o) => o.id === id);

  if (orderIndex === -1) {
    throw new Error('Order not found');
  }

  // Update order status in mock data
  const updatedMockOrder = {
    ...mockOrders[orderIndex],
    status,
    paymentStatus:
      status === 'completed'
        ? 'completed'
        : (mockOrders[orderIndex] as any).paymentStatus,
  } as LegacyOrder;

  mockOrders[orderIndex] = updatedMockOrder as any;

  // Transform to new format
  const newOrder: Order = {
    orderId: parseInt(updatedMockOrder.id) || 0,
    orderCode: updatedMockOrder.id,
    orderType: updatedMockOrder.type,
    userId: updatedMockOrder.memberId,
    counterId: '0001',
    staffId: updatedMockOrder.createdBy || '',
    quantity: 1,
    pricePerUnit: updatedMockOrder.amount,
    currencyUnit: 'USD' as any,
    orderStatus: status.toUpperCase() as any,
    notes: updatedMockOrder.notes || '',
    metadata: null,
    createdAt: updatedMockOrder.date,
    updatedAt: new Date().toISOString(),
  };

  const transformedOrder = transformBackendOrder(newOrder);

  return {
    data: transformedOrder,
    success: true,
    message: 'Order status updated successfully in mock',
  };
};

/**
 * Main function to update order status - switches between mock and real API
 */
export const updateOrderStatus = async (
  id: string,
  status: string,
): Promise<ApiResponse<OrderWithComputed>> => {
  if (shouldUseMockData()) {
    return updateOrderStatusFromMock(id, status);
  } else {
    return updateOrderStatusFromAPI(id, status);
  }
};
