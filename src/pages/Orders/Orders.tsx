import {
  EyeOutlined,
  EditOutlined,
  ReloadOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  SearchOutlined,
  FilterOutlined,
  ClearOutlined,
} from '@ant-design/icons';
import {
  Select,
  Space,
  Button,
  Card,
  Typography,
  Avatar,
  Statistic,
  Row,
  Col,
  DatePicker,
  Tooltip,
} from 'antd';
import Search from 'antd/es/input/Search';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { OrderTypeTag, CurrencyTag } from '@/components';
import GalaxyTable from '@/components/WeAreTableGalaxy/GalaxyTable';
import { useOrdersWithFilters, useUpdateOrderStatus } from '@/hooks/useOrders.ts';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import OrderDetailsModal from '@/pages/Orders/modals/OrderDetailsModal';
import EditOrderModal from '@/pages/Orders/modals/EditOrderModal';
import { useUserStore } from '@/stores';
import type { Order } from '@/types';
import { OrderTypeEnums, OrderStatusEnums, CurrencyCodeEnums } from '@/utils/enums';
import { formatOrderAmount } from '@/utils/currencyUtils';
import { formatDate } from '@/utils/tableUtils.ts';

const { Option } = Select;
const { Title, Text } = Typography;
const { RangePicker } = DatePicker;

const Orders: React.FC = () => {
  const { t } = useTranslation('orders');
  const { isMobile } = useResponsive();
  const { isDark } = useUserStore();

  // Local state for UI only
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<Order | null>(null);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // UI input states (separate from API filter logic)
  const [statusValue, setStatusValue] = useState<string | undefined>();
  const [orderTypeValue, setOrderTypeValue] = useState<string | undefined>();
  const [currencyValue, setCurrencyValue] = useState<string | undefined>();
  const [userSearchValue, setUserSearchValue] = useState<string>('');
  const [counterSearchValue, setCounterSearchValue] = useState<string>('');
  const [staffSearchValue, setStaffSearchValue] = useState<string>('');
  const [dateRangeValue, setDateRangeValue] = useState<any>(null);

  // Responsive values
  const avatarSize = useResponsiveValue({
    xs: 32,
    sm: 36,
    md: 40,
    lg: 40,
    xl: 40,
    '2xl': 40,
  });

  const {
    data: ordersResponse,
    isLoading,
    error,
    refetch,
    // Filter methods
    filterByStatus,
    filterByOrderType,
    filterByUser,
    filterByCounter,
    filterByStaff,
    filterByCurrency,
    filterByDateRange,
    // Clear methods
    clearStatusFilter,
    clearOrderTypeFilter,
    clearUserFilter,
    clearCounterFilter,
    clearStaffFilter,
    clearCurrencyFilter,
    clearDateFilter,
    clearFilters,
    // State
    filters,
    hasActiveFiltering,
    // Pagination
    updatePage,
    updatePageSize,
    page,
    pageSize,
  } = useOrdersWithFilters();

  const updateOrderStatusMutation = useUpdateOrderStatus();

  // Filter handlers - manage both UI state and API filters
  const handleStatusFilter = (value: string) => {
    setStatusValue(value);
    if (value) {
      filterByStatus(value);
    } else {
      clearStatusFilter();
    }
  };

  const handleOrderTypeFilter = (value: string) => {
    setOrderTypeValue(value);
    if (value) {
      filterByOrderType(value);
    } else {
      clearOrderTypeFilter();
    }
  };

  const handleCurrencyFilter = (value: string) => {
    setCurrencyValue(value);
    if (value) {
      filterByCurrency(value);
    } else {
      clearCurrencyFilter();
    }
  };

  const handleUserSearch = (value: string) => {
    setUserSearchValue(value);
    if (value.trim()) {
      filterByUser(value.trim());
    } else {
      clearUserFilter();
    }
  };

  const handleCounterFilter = (value: string) => {
    setCounterSearchValue(value);
    if (value.trim()) {
      filterByCounter(value.trim());
    } else {
      clearCounterFilter();
    }
  };

  const handleStaffSearch = (value: string) => {
    setStaffSearchValue(value);
    if (value.trim()) {
      filterByStaff(value.trim());
    } else {
      clearStaffFilter();
    }
  };

  const handleDateRangeFilter = (dates: any) => {
    setDateRangeValue(dates);
    if (dates && dates.length === 2) {
      const [startDate, endDate] = dates;
      filterByDateRange(
        startDate.format('YYYY-MM-DD'),
        endDate.format('YYYY-MM-DD')
      );
    } else {
      clearDateFilter();
    }
  };



  const handleClearAllFilters = () => {
    // Clear API filters
    clearFilters();

    // Clear UI input states
    setStatusValue(undefined);
    setOrderTypeValue(undefined);
    setCurrencyValue(undefined);
    setUserSearchValue('');
    setCounterSearchValue('');
    setStaffSearchValue('');
    setDateRangeValue(null);

    setShowAdvancedFilters(false);
  };

  // Modal handlers
  const handleViewOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowDetailsModal(true);
  };

  const handleEditOrder = (order: Order) => {
    setSelectedOrder(order);
    setShowEditModal(true);
  };

  const handleCloseDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedOrder(null);
  };

  const handleCloseEditModal = () => {
    setShowEditModal(false);
    setSelectedOrder(null);
  };

  // Pagination handler
  const handlePaginationChange = (newPage: number, newPageSize: number) => {
    updatePage(newPage);
    if (newPageSize !== pageSize) {
      updatePageSize(newPageSize);
    }
  };

  const handleStatusUpdate = async (
    orderId: number,
    newStatus: string,
  ) => {
    try {
      await updateOrderStatusMutation.mutateAsync({
        orderId: orderId,
        status: newStatus,
      });
    } catch (error) {
      console.log(error);
      // Error is handled by the mutation
    }
  };

  const columns = [
    {
      title: t('type'),
      dataIndex: 'orderType',
      key: 'orderType',
      align: 'center',
      render: (type: OrderTypeEnums) => <OrderTypeTag type={type} />,
    },
    {
      title: t('orderDetails'),
      dataIndex: 'orderCode',
      key: 'orderDetails',
      render: (orderCode: string, record: Order) => (
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <Avatar
            className='bg-gradient-to-br from-green-400 to-green-600 text-white font-medium flex-shrink-0'
            icon={<ShoppingCartOutlined />}
            size={avatarSize}
          />
          <div className='min-w-0 flex-1'>
            <Text
              className={`font-medium text-gray-900 block ${isMobile ? 'text-sm' : ''} truncate`}
            >
              {orderCode}
            </Text>
            <Text
              className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 truncate`}
            >
              User: {record.userId.slice(0, 8)}...
            </Text>
            <Text
              className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 truncate`}
            >
              Counter: {record.counterId}
            </Text>
          </div>
        </div>
      ),
      width: isMobile ? 180 : undefined,
    },
    {
      title: t('amount'),
      dataIndex: 'pricePerUnit',
      key: 'amount',
      render: (pricePerUnit: number, record: Order) => {
        const totalPrice = record.quantity * record.pricePerUnit;
        return (
          <div className='text-right'>
            <Text
              className={`font-bold ${isMobile ? 'text-base' : 'text-lg'} text-gray-900 block`}
            >
              {formatOrderAmount(totalPrice, record.currencyUnit)}
            </Text>
            {!isMobile && (
              <div className='text-sm text-gray-500'>
                <div>{record.quantity} × {formatOrderAmount(record.pricePerUnit, record.currencyUnit)}</div>
                <CurrencyTag currency={record.currencyUnit} />
              </div>
            )}
          </div>
        );
      },
      sorter: (a: Order, b: Order) => (a.quantity * a.pricePerUnit) - (b.quantity * b.pricePerUnit),
      width: isMobile ? 120 : undefined,
    },
    {
      title: t('status'),
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      filters: [
        { text: 'Pending', value: 'PENDING' },
        { text: 'Confirmed', value: 'CONFIRMED' },
        { text: 'Processing', value: 'PROCESSING' },
        { text: 'Completed', value: 'COMPLETED' },
        { text: 'Cancelled', value: 'CANCELLED' },
        { text: 'Rejected', value: 'REJECTED' },
      ],
      onFilter: (value: any, record: Order) => {
        return record.orderStatus === value;
      },
      render: (status: OrderStatusEnums, record: Order) => (
        <Select
          value={status}
          style={{ width: isMobile ? 120 : 140 }}
          size={isMobile ? 'small' : 'middle'}
          onChange={(newStatus: OrderStatusEnums) =>
            handleStatusUpdate(record.orderId, newStatus)
          }
          loading={updateOrderStatusMutation.isPending}
          className='rounded-lg'
        >
          <Option value={OrderStatusEnums.PENDING}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-orange-400 rounded-full'></div>
              Pending
            </div>
          </Option>
          <Option value={OrderStatusEnums.CONFIRMED}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-blue-400 rounded-full'></div>
              Confirmed
            </div>
          </Option>
          <Option value={OrderStatusEnums.PROCESSING}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-purple-400 rounded-full'></div>
              Processing
            </div>
          </Option>
          <Option value={OrderStatusEnums.COMPLETED}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-green-400 rounded-full'></div>
              Completed
            </div>
          </Option>
          <Option value={OrderStatusEnums.CANCELLED}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-gray-400 rounded-full'></div>
              Cancelled
            </div>
          </Option>
          <Option value={OrderStatusEnums.REJECTED}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-red-400 rounded-full'></div>
              Rejected
            </div>
          </Option>
        </Select>
      ),
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => (
        <Text className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
          {quantity}
        </Text>
      ),
      width: isMobile ? 80 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('date'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => (
        <div>
          <Text className={`text-gray-900 block ${isMobile ? 'text-xs' : ''}`}>
            {formatDate(date).split(',')[0]}
          </Text>
          {!isMobile && (
            <Text className='text-sm text-gray-500'>
              {formatDate(date).split(',')[1]}
            </Text>
          )}
        </div>
      ),
      sorter: (a: Order, b: Order) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: 'Staff ID',
      dataIndex: 'staffId',
      key: 'staffId',
      render: (staffId: string) => (
        <div
          className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'} ${isDark ? 'bg-slate-600' : 'bg-slate-300'} ${isMobile ? 'px-2 py-1' : 'px-3 py-1'} rounded-lg inline-block`}
        >
          {isMobile ? staffId.slice(0, 8) : staffId.slice(0, 12)}...
        </div>
      ),
      width: isMobile ? 80 : undefined,
      responsive: isMobile ? ['xl'] : undefined,
    },
    {
      title: t('actions'),
      key: 'actions',
      width: isMobile ? 60 : 120,
      render: (_: any, record: Order) => (
        <Space>
          <Button
            type='text'
            icon={<EyeOutlined />}
            className='hover:bg-blue-50 hover:text-blue-600 rounded-lg'
            onClick={() => handleViewOrder(record)}
            size={isMobile ? 'small' : 'middle'}
          >
            {!isMobile && 'View'}
          </Button>
          <Button
            type='text'
            icon={<EditOutlined />}
            className='hover:bg-green-50 hover:text-green-600 rounded-lg'
            onClick={() => handleEditOrder(record)}
            size={isMobile ? 'small' : 'middle'}
          >
            {!isMobile && 'Edit'}
          </Button>
        </Space>
      ),
    },
  ];



  const orders = ordersResponse?.data?.data || [];
  const total = ordersResponse?.data?.total || 0;
  const completedOrders = orders.filter((o) => o.orderStatus === OrderStatusEnums.COMPLETED).length;
  const pendingOrders = orders.filter((o) => o.orderStatus === OrderStatusEnums.PENDING).length;
  const totalRevenue = orders
    .filter((o) => o.orderStatus === OrderStatusEnums.COMPLETED)
    .reduce((sum, o) => sum + (o.quantity * o.pricePerUnit), 0);

  return (
    <div
      className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6 lg:space-y-8'}`}
    >
      {/* Header Section */}
      <div
        className={`${
          isDark
            ? 'bg-gradient-to-r from-green-900 to-emerald-850 border-green-800'
            : 'bg-gradient-to-r from-green-200 to-emerald-100 border-green-200 text-green-900'
        } ${isMobile ? 'rounded-xl p-4' : 'rounded-2xl p-6 lg:p-8'} border`}
      >
        <div
          className={`flex ${isMobile ? 'flex-col gap-4' : 'items-center justify-between'}`}
        >
          <div className={`flex items-center ${isMobile ? 'gap-3' : 'gap-4'}`}>
            <div
              className={`${isMobile ? 'w-10 h-10' : 'w-12 h-12'} bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center flex-shrink-0`}
            >
              <ShoppingCartOutlined
                className={`text-white ${isMobile ? 'text-lg' : 'text-xl'}`}
              />
            </div>
            <div className='min-w-0 flex-1'>
              <Title
                level={2}
                className={`text-gray-900 mb-1 font-bold ${isMobile ? 'text-lg' : ''}`}
              >
                {t('title')}
              </Title>
              <Text
                className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-base'}`}
              >
                {t('description')}
              </Text>
            </div>
          </div>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
            size={isMobile ? 'middle' : 'large'}
            className={`rounded-lg ${isMobile ? 'h-10 px-4 w-full' : 'h-12 px-6'} font-medium shadow-lg hover:shadow-xl transition-all`}
          >
            {t('refresh')}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <Row
        gutter={[
          { xs: 12, sm: 16, lg: 24 },
          { xs: 12, sm: 16, lg: 24 },
        ]}
      >
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('totalOrders')}
                </span>
              }
              value={total}
              prefix={<ShoppingCartOutlined className='text-primary' />}
              valueStyle={{
                color: '#1890ff',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('completed')}
                </span>
              }
              value={completedOrders}
              prefix={<CheckCircleOutlined className='text-green-500' />}
              valueStyle={{
                color: '#52c41a',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('pending')}
                </span>
              }
              value={pendingOrders}
              prefix={<ClockCircleOutlined className='text-orange-500' />}
              valueStyle={{
                color: '#faad14',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('revenue')}
                </span>
              }
              value={totalRevenue}
              prefix={<DollarOutlined className='text-purple-500' />}
              valueStyle={{
                color: '#e15ae1',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
              formatter={(value) => `$${Number(value).toLocaleString()}`}
            />
          </Card>
        </Col>
      </Row>

      {/* Search and Filters */}
      <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-0 shadow-lg rounded-2xl overflow-hidden">
        <div className="p-6">
          {/* Header Section */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-blue-100 rounded-xl">
                <SearchOutlined className="text-blue-600 text-xl" />
              </div>
              <div>
                <Title level={4} className="!mb-1 !text-gray-800">
                  Search & Filter Orders
                </Title>
                <Text className="text-gray-600">
                  Find orders using multiple criteria
                </Text>
              </div>
            </div>

            <div className="flex items-center gap-3">
              {hasActiveFiltering && (
                <Button
                  icon={<ClearOutlined />}
                  onClick={handleClearAllFilters}
                  className="bg-red-50 border-red-200 text-red-600 hover:bg-red-100 hover:border-red-300"
                  size="large"
                >
                  Clear All
                </Button>
              )}
              <Button
                icon={<FilterOutlined />}
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                type={showAdvancedFilters ? 'primary' : 'default'}
                size="large"
                className={showAdvancedFilters ? 'bg-blue-600' : 'bg-white border-gray-300 hover:border-blue-400'}
              >
                {showAdvancedFilters ? 'Hide Filters' : 'More Filters'}
              </Button>
            </div>
          </div>

          {/* Quick Filters */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
            {/* Status Filter */}
            <div className="space-y-2">
              <Text className="text-sm font-medium text-gray-700">Order Status</Text>
              <Select
                placeholder="All Statuses"
                allowClear
                value={statusValue}
                onChange={handleStatusFilter}
                size="large"
                className="w-full"
                style={{ borderRadius: '12px' }}
              >
                <Option value={OrderStatusEnums.PENDING}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-orange-400 rounded-full'></div>
                    Pending
                  </div>
                </Option>
                <Option value={OrderStatusEnums.CONFIRMED}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-blue-400 rounded-full'></div>
                    Confirmed
                  </div>
                </Option>
                <Option value={OrderStatusEnums.PROCESSING}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-purple-400 rounded-full'></div>
                    Processing
                  </div>
                </Option>
                <Option value={OrderStatusEnums.COMPLETED}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-green-400 rounded-full'></div>
                    Completed
                  </div>
                </Option>
                <Option value={OrderStatusEnums.CANCELLED}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-gray-400 rounded-full'></div>
                    Cancelled
                  </div>
                </Option>
                <Option value={OrderStatusEnums.REJECTED}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-red-400 rounded-full'></div>
                    Rejected
                  </div>
                </Option>
              </Select>
            </div>

            {/* Order Type Filter */}
            <div className="space-y-2">
              <Text className="text-sm font-medium text-gray-700">Order Type</Text>
              <Select
                placeholder="All Types"
                allowClear
                value={orderTypeValue}
                onChange={handleOrderTypeFilter}
                size="large"
                className="w-full"
                style={{ borderRadius: '12px' }}
              >
                <Option value={OrderTypeEnums.BUY}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-green-400 rounded-full'></div>
                    Buy
                  </div>
                </Option>
                <Option value={OrderTypeEnums.SELL}>
                  <div className='flex items-center gap-2'>
                    <div className='w-2 h-2 bg-red-400 rounded-full'></div>
                    Sell
                  </div>
                </Option>
              </Select>
            </div>

            {/* Currency Filter */}
            <div className="space-y-2">
              <Text className="text-sm font-medium text-gray-700">Currency</Text>
              <Select
                placeholder="All Currencies"
                allowClear
                value={currencyValue}
                onChange={handleCurrencyFilter}
                size="large"
                className="w-full"
                style={{ borderRadius: '12px' }}
              >
                {Object.values(CurrencyCodeEnums).map(currency => (
                  <Option key={currency} value={currency}>
                    <div className="flex items-center gap-2">
                      <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                        {currency}
                      </span>
                    </div>
                  </Option>
                ))}
              </Select>
            </div>

            {/* User Search */}
            <div className="space-y-2">
              <Text className="text-sm font-medium text-gray-700">User ID</Text>
              <Search
                placeholder="Search user..."
                allowClear
                value={userSearchValue}
                onChange={(e) => setUserSearchValue(e.target.value)}
                onSearch={handleUserSearch}
                size="large"
                className="w-full"
                style={{ borderRadius: '12px' }}
              />
            </div>
          </div>

          {/* Advanced Filters */}
          {showAdvancedFilters && (
            <div className="border-t border-gray-200 pt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Counter Search */}
                <div className="space-y-2">
                  <Text className="text-sm font-medium text-gray-700">Counter ID</Text>
                  <Search
                    placeholder="Search counter..."
                    allowClear
                    value={counterSearchValue}
                    onChange={(e) => setCounterSearchValue(e.target.value)}
                    onSearch={handleCounterFilter}
                    size="large"
                    className="w-full"
                    style={{ borderRadius: '12px' }}
                  />
                </div>

                {/* Staff Search */}
                <div className="space-y-2">
                  <Text className="text-sm font-medium text-gray-700">Staff ID</Text>
                  <Search
                    placeholder="Search staff..."
                    allowClear
                    value={staffSearchValue}
                    onChange={(e) => setStaffSearchValue(e.target.value)}
                    onSearch={handleStaffSearch}
                    size="large"
                    className="w-full"
                    style={{ borderRadius: '12px' }}
                  />
                </div>

                {/* Date Range - spans both columns */}
                <div className="space-y-2 md:col-span-2">
                  <Text className="text-sm font-medium text-gray-700">Date Range</Text>
                  <RangePicker
                    placeholder={['Start Date', 'End Date']}
                    value={dateRangeValue}
                    onChange={handleDateRangeFilter}
                    size="large"
                    className="w-full"
                    style={{ borderRadius: '12px' }}
                  />
                </div>
              </div>
            </div>
          )}
        </div>
      </Card>
      {/* Orders Table */}
      <Card
        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card overflow-hidden`}
      >
        <GalaxyTable
          data={orders}
          columns={columns}
          loading={isLoading}
          customSort={true}
          pagination={{
            current: page,
            pageSize: pageSize,
            total,
            onChange: handlePaginationChange,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: !isMobile
              ? (total, range) =>
                  `${range[0]}-${range[1]} ${t('of')} ${total} ${t('items')}`
              : undefined,
          }}
          rowKey='orderCode'
          scroll={isMobile ? { x: 1200 } : undefined}
        />
      </Card>

      {/* Order Details Modal */}
      <OrderDetailsModal
        visible={showDetailsModal}
        order={selectedOrder}
        onClose={handleCloseDetailsModal}
        onEdit={(order) => {
          setSelectedOrder(order);
          setShowDetailsModal(false);
          setShowEditModal(true);
        }}
        onUpdateStatus={(orderId, status) => {
          handleStatusUpdate(orderId, status);
        }}
      />

      {/* Edit Order Modal */}
      <EditOrderModal
        visible={showEditModal}
        order={selectedOrder}
        onCancel={handleCloseEditModal}
      />
    </div>
  );
};

export default Orders;
