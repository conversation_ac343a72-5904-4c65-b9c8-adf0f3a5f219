import {
  EyeOutlined,
  ReloadOutlined,
  ShoppingCartOutlined,
  DollarOutlined,
  ClockCircleOutlined,
  CheckCircleOutlined,
  SearchOutlined,
} from '@ant-design/icons';
import {
  Select,
  Space,
  Tag,
  Button,
  Alert,
  Card,
  Typography,
  Avatar,
  Statistic,
  Row,
  Col,
} from 'antd';
import Search from 'antd/es/input/Search';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { OrderTypeTag, OrderStatusTag, CurrencyTag } from '@/components';
import GalaxyTable from '@/components/WeAreTableGalaxy/GalaxyTable';
import { useOrdersWithFilters, useUpdateOrderStatus } from '@/hooks/useOrders.ts';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import OrderDetailsModal from '@/pages/Orders/modals/OrderDetailsModal';
import { useUserStore } from '@/stores';
import type { Order } from '@/types';
import { OrderTypeEnums, OrderStatusEnums } from '@/utils/enums';
import { formatOrderAmount, getCurrencySymbol } from '@/utils/orderTransformers';
import {
  formatDate,
  formatCurrency,
  getStatusColor,
} from '@/utils/tableUtils.ts';

const { Option } = Select;
const { Title, Text } = Typography;

const Orders: React.FC = () => {
  const { t } = useTranslation('orders');
  const { isMobile } = useResponsive();
  const { isDark } = useUserStore();
  const [statusFilter, setStatusFilter] = useState<string>('');

  const [searchTerm, setSearchTerm] = useState('');
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const [selectedOrder, setSelectedOrder] = useState<OrderWithComputed | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  // Responsive values
  const searchWidth = useResponsiveValue({
    xs: '100%',
    sm: '100%',
    md: '320px',
    lg: '420px',
    xl: '420px',
    '2xl': '420px',
  });

  const selectWidth = useResponsiveValue({
    xs: '100%',
    sm: '100%',
    md: '180px',
    lg: '200px',
    xl: '200px',
    '2xl': '200px',
  });

  const avatarSize = useResponsiveValue({
    xs: 32,
    sm: 36,
    md: 40,
    lg: 40,
    xl: 40,
    '2xl': 40,
  });

  const {
    data: ordersResponse,
    isLoading,
    error,
    refetch,
    // Filter methods
    filterByStatus,
    filterByUser,
    clearStatusFilter,
    clearUserFilter,
    // Pagination
    updatePage,
    updatePageSize,
    page,
    pageSize,
  } = useOrdersWithFilters();

  const updateOrderStatusMutation = useUpdateOrderStatus();

  const handleStatusFilter = (value: string) => {
    setStatusFilter(value);
    if (value) {
      filterByStatus(value);
    } else {
      clearStatusFilter();
    }
  };

  const handlePaginationChange = (newPage: number, newPageSize: number) => {
    updatePage(newPage);
    if (newPageSize !== pageSize) {
      updatePageSize(newPageSize);
    }
  };

  const handleViewOrder = (order: OrderWithComputed) => {
    setSelectedOrder(order);
    setShowDetailsModal(true);
  };

  const handleCloseDetailsModal = () => {
    setShowDetailsModal(false);
    setSelectedOrder(null);
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    if (value.trim()) {
      filterByUser(value.trim());
    } else {
      clearUserFilter();
    }
  };

  const handleStatusUpdate = async (
    orderId: string,
    newStatus: string,
  ) => {
    try {
      await updateOrderStatusMutation.mutateAsync({
        id: orderId,
        status: newStatus,
      });
    } catch (error) {
      console.log(error);
      // Error is handled by the mutation
    }
  };

  const columns = [
    {
      title: t('type'),
      dataIndex: 'orderType',
      key: 'orderType',
      align: 'center',
      render: (type: OrderTypeEnums) => <OrderTypeTag type={type} />,
    },
    {
      title: t('orderDetails'),
      dataIndex: 'orderCode',
      key: 'orderDetails',
      render: (orderCode: string, record: OrderWithComputed) => (
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <Avatar
            className='bg-gradient-to-br from-green-400 to-green-600 text-white font-medium flex-shrink-0'
            icon={<ShoppingCartOutlined />}
            size={avatarSize}
          />
          <div className='min-w-0 flex-1'>
            <Text
              className={`font-medium text-gray-900 block ${isMobile ? 'text-sm' : ''} truncate`}
            >
              {orderCode}
            </Text>
            <Text
              className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 truncate`}
            >
              User: {record.userId.slice(0, 8)}...
            </Text>
            <Text
              className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500 truncate`}
            >
              Counter: {record.counterId}
            </Text>
          </div>
        </div>
      ),
      width: isMobile ? 180 : undefined,
    },
    {
      title: t('amount'),
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      render: (totalPrice: number, record: OrderWithComputed) => (
        <div className='text-right'>
          <Text
            className={`font-bold ${isMobile ? 'text-base' : 'text-lg'} text-gray-900 block`}
          >
            {formatOrderAmount(totalPrice, record.currencyUnit)}
          </Text>
          {!isMobile && (
            <div className='text-sm text-gray-500'>
              <div>{record.quantity} × {formatOrderAmount(record.pricePerUnit, record.currencyUnit)}</div>
              <CurrencyTag currency={record.currencyUnit} />
            </div>
          )}
        </div>
      ),
      sorter: (a: OrderWithComputed, b: OrderWithComputed) => a.totalPrice - b.totalPrice,
      width: isMobile ? 120 : undefined,
    },
    {
      title: t('status'),
      dataIndex: 'orderStatus',
      key: 'orderStatus',
      filters: [
        { text: 'Pending', value: 'PENDING' },
        { text: 'Confirmed', value: 'CONFIRMED' },
        { text: 'Processing', value: 'PROCESSING' },
        { text: 'Completed', value: 'COMPLETED' },
        { text: 'Cancelled', value: 'CANCELLED' },
        { text: 'Rejected', value: 'REJECTED' },
      ],
      onFilter: (value: any, record: OrderWithComputed) => {
        return record.orderStatus === value;
      },
      render: (status: OrderStatusEnums, record: OrderWithComputed) => (
        <Select
          value={status}
          style={{ width: isMobile ? 120 : 140 }}
          size={isMobile ? 'small' : 'middle'}
          onChange={(newStatus: OrderStatusEnums) =>
            handleStatusUpdate(record.orderCode, newStatus)
          }
          loading={updateOrderStatusMutation.isPending}
          className='rounded-lg'
        >
          <Option value={OrderStatusEnums.PENDING}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-orange-400 rounded-full'></div>
              Pending
            </div>
          </Option>
          <Option value={OrderStatusEnums.CONFIRMED}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-blue-400 rounded-full'></div>
              Confirmed
            </div>
          </Option>
          <Option value={OrderStatusEnums.PROCESSING}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-purple-400 rounded-full'></div>
              Processing
            </div>
          </Option>
          <Option value={OrderStatusEnums.COMPLETED}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-green-400 rounded-full'></div>
              Completed
            </div>
          </Option>
          <Option value={OrderStatusEnums.CANCELLED}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-gray-400 rounded-full'></div>
              Cancelled
            </div>
          </Option>
          <Option value={OrderStatusEnums.REJECTED}>
            <div className='flex items-center gap-2'>
              <div className='w-2 h-2 bg-red-400 rounded-full'></div>
              Rejected
            </div>
          </Option>
        </Select>
      ),
    },
    {
      title: 'Quantity',
      dataIndex: 'quantity',
      key: 'quantity',
      render: (quantity: number) => (
        <Text className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
          {quantity}
        </Text>
      ),
      width: isMobile ? 80 : undefined,
      responsive: isMobile ? ['md'] : undefined,
    },
    {
      title: t('date'),
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => (
        <div>
          <Text className={`text-gray-900 block ${isMobile ? 'text-xs' : ''}`}>
            {formatDate(date).split(',')[0]}
          </Text>
          {!isMobile && (
            <Text className='text-sm text-gray-500'>
              {formatDate(date).split(',')[1]}
            </Text>
          )}
        </div>
      ),
      sorter: (a: OrderWithComputed, b: OrderWithComputed) =>
        new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime(),
      width: isMobile ? 100 : undefined,
      responsive: isMobile ? ['lg'] : undefined,
    },
    {
      title: 'Staff ID',
      dataIndex: 'staffId',
      key: 'staffId',
      render: (staffId: string) => (
        <div
          className={`font-mono ${isMobile ? 'text-xs' : 'text-sm'} ${isDark ? 'bg-slate-600' : 'bg-slate-300'} ${isMobile ? 'px-2 py-1' : 'px-3 py-1'} rounded-lg inline-block`}
        >
          {isMobile ? staffId.slice(0, 8) : staffId.slice(0, 12)}...
        </div>
      ),
      width: isMobile ? 80 : undefined,
      responsive: isMobile ? ['xl'] : undefined,
    },
    {
      title: t('actions'),
      key: 'actions',
      width: isMobile ? 60 : 120,
      render: (_: any, record: OrderWithComputed) => (
        <Space>
          <Button
            type='text'
            icon={<EyeOutlined />}
            className='hover:bg-primary/10 hover:text-primary rounded-lg'
            onClick={() => handleViewOrder(record)}
            size={isMobile ? 'small' : 'middle'}
          >
            {!isMobile && t('view')}
          </Button>
        </Space>
      ),
    },
  ];

  if (error) {
    return (
      <Alert
        message='Error'
        description='Failed to load orders'
        type='error'
        showIcon
        className='rounded-lg'
      />
    );
  }

  const orders = ordersResponse?.data?.data || [];
  const total = ordersResponse?.data?.total || 0;
  const completedOrders = orders.filter((o) => o.orderStatus === OrderStatusEnums.COMPLETED).length;
  const pendingOrders = orders.filter((o) => o.orderStatus === OrderStatusEnums.PENDING).length;
  const totalRevenue = orders
    .filter((o) => o.orderStatus === OrderStatusEnums.COMPLETED)
    .reduce((sum, o) => sum + o.totalPrice, 0);

  return (
    <div
      className={`space-y-4 ${isMobile ? 'sm:space-y-4' : 'sm:space-y-6 lg:space-y-8'}`}
    >
      {/* Header Section */}
      <div
        className={`${
          isDark
            ? 'bg-gradient-to-r from-green-900 to-emerald-850 border-green-800'
            : 'bg-gradient-to-r from-green-200 to-emerald-100 border-green-200 text-green-900'
        } ${isMobile ? 'rounded-xl p-4' : 'rounded-2xl p-6 lg:p-8'} border`}
      >
        <div
          className={`flex ${isMobile ? 'flex-col gap-4' : 'items-center justify-between'}`}
        >
          <div className={`flex items-center ${isMobile ? 'gap-3' : 'gap-4'}`}>
            <div
              className={`${isMobile ? 'w-10 h-10' : 'w-12 h-12'} bg-gradient-to-br from-green-500 to-emerald-600 rounded-xl flex items-center justify-center flex-shrink-0`}
            >
              <ShoppingCartOutlined
                className={`text-white ${isMobile ? 'text-lg' : 'text-xl'}`}
              />
            </div>
            <div className='min-w-0 flex-1'>
              <Title
                level={2}
                className={`text-gray-900 mb-1 font-bold ${isMobile ? 'text-lg' : ''}`}
              >
                {t('title')}
              </Title>
              <Text
                className={`text-gray-600 ${isMobile ? 'text-sm' : 'text-base'}`}
              >
                {t('description')}
              </Text>
            </div>
          </div>
          <Button
            icon={<ReloadOutlined />}
            onClick={() => refetch()}
            loading={isLoading}
            size={isMobile ? 'middle' : 'large'}
            className={`rounded-lg ${isMobile ? 'h-10 px-4 w-full' : 'h-12 px-6'} font-medium shadow-lg hover:shadow-xl transition-all`}
          >
            {t('refresh')}
          </Button>
        </div>
      </div>

      {/* Stats Cards */}
      <Row
        gutter={[
          { xs: 12, sm: 16, lg: 24 },
          { xs: 12, sm: 16, lg: 24 },
        ]}
      >
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('totalOrders')}
                </span>
              }
              value={total}
              prefix={<ShoppingCartOutlined className='text-primary' />}
              valueStyle={{
                color: '#1890ff',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('completed')}
                </span>
              }
              value={completedOrders}
              prefix={<CheckCircleOutlined className='text-green-500' />}
              valueStyle={{
                color: '#52c41a',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('pending')}
                </span>
              }
              value={pendingOrders}
              prefix={<ClockCircleOutlined className='text-orange-500' />}
              valueStyle={{
                color: '#faad14',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card
            className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 text-center ${isDark ? 'statsDarkCard' : 'shadow-card'} `}
          >
            <Statistic
              title={
                <span className={isDark ? 'text-white' : 'text-black'}>
                  {t('revenue')}
                </span>
              }
              value={totalRevenue}
              prefix={<DollarOutlined className='text-purple-500' />}
              valueStyle={{
                color: '#e15ae1',
                fontSize: isMobile ? '20px' : '28px',
                fontWeight: 'bold',
              }}
              formatter={(value) => formatCurrency(Number(value))}
            />
          </Card>
        </Col>
      </Row>

      {/* Search and Filters */}
      <Card
        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}
      >
        <div
          className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row'} ${isMobile ? 'gap-3' : 'gap-4'} items-start sm:items-center justify-between`}
        >
          <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
            <SearchOutlined
              className={`text-gray-400 ${isMobile ? 'text-base' : 'text-lg'}`}
            />
            <div>
              <Text
                className={`font-medium text-gray-900 block ${isMobile ? 'text-sm' : ''}`}
              >
                {t('searchOrders')}
              </Text>
              <Text
                className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}
              >
                {t('searchDescription')}
              </Text>
            </div>
          </div>
          <div
            className={`flex ${isMobile ? 'flex-col w-full gap-2' : 'gap-2'}`}
          >
            <Select
              placeholder={t('statusPlaceholder')}
              allowClear
              style={{ width: selectWidth }}
              value={statusFilter || undefined}
              onChange={handleStatusFilter}
              size={isMobile ? 'middle' : 'large'}
              className='rounded-lg'
            >
              <Option value={OrderStatusEnums.PENDING}>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-orange-400 rounded-full'></div>
                  Pending
                </div>
              </Option>
              <Option value={OrderStatusEnums.CONFIRMED}>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-blue-400 rounded-full'></div>
                  Confirmed
                </div>
              </Option>
              <Option value={OrderStatusEnums.PROCESSING}>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-purple-400 rounded-full'></div>
                  Processing
                </div>
              </Option>
              <Option value={OrderStatusEnums.COMPLETED}>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-green-400 rounded-full'></div>
                  Completed
                </div>
              </Option>
              <Option value={OrderStatusEnums.CANCELLED}>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-gray-400 rounded-full'></div>
                  Cancelled
                </div>
              </Option>
              <Option value={OrderStatusEnums.REJECTED}>
                <div className='flex items-center gap-2'>
                  <div className='w-2 h-2 bg-red-400 rounded-full'></div>
                  Rejected
                </div>
              </Option>
            </Select>
            <Search
              placeholder={t('searchPlaceholder')}
              allowClear
              size={isMobile ? 'middle' : 'large'}
              style={{ width: searchWidth }}
              onSearch={handleSearch}
              className='rounded-lg'
            />
          </div>
        </div>
      </Card>
      {/* Orders Table */}
      <Card
        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card overflow-hidden`}
      >
        <GalaxyTable
          data={orders}
          columns={columns}
          loading={isLoading}
          customSort={true}
          pagination={{
            current: page,
            pageSize: pageSize,
            total,
            onChange: handlePaginationChange,
            showSizeChanger: !isMobile,
            showQuickJumper: !isMobile,
            showTotal: !isMobile
              ? (total, range) =>
                  `${range[0]}-${range[1]} ${t('of')} ${total} ${t('items')}`
              : undefined,
          }}
          rowKey='orderCode'
          scroll={isMobile ? { x: 1200 } : undefined}
        />
      </Card>

      {/* Order Details Modal */}
      <OrderDetailsModal
        visible={showDetailsModal}
        order={selectedOrder}
        onClose={handleCloseDetailsModal}
        onEdit={(order) => {
          // TODO: Implement edit functionality
          console.log('Edit order:', order);
        }}
        onUpdateStatus={(orderId, status) => {
          // TODO: Implement status update
          console.log('Update status:', orderId, status);
        }}
      />
    </div>
  );
};

export default Orders;
