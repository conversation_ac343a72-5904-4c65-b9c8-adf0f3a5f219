import {
  ShoppingCartOutlined,
  UserOutlined,
  DollarOutlined,
  CalendarOutlined,
  CreditCardOutlined,
  QrcodeOutlined,
  CloseOutlined,
  EditOutlined,
  PrinterOutlined,
  CopyOutlined,
  CheckCircleOutlined,
  ClockCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  FileTextOutlined,
  TagOutlined,
  BankOutlined,
} from '@ant-design/icons';
import {
  Modal,
  Descriptions,
  Tag,
  Typography,
  Card,
  Row,
  Col,
  Avatar,
  Divider,
  Space,
  Button,
  Table,
  QRCode,
  Steps,
  Timeline,
  Alert,
  type Breakpoint,
} from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { OrderStatusTag, OrderTypeTag } from '@/components';
import { useResponsive, useResponsiveValue } from '@/hooks/useResponsive.ts';
import { useUserStore } from '@/stores';
import type { Order, OrderItem } from '@/types';
import { formatDateTime } from '@/utils/tableUtils.ts';
import { formatOrderAmount } from '@/utils/currencyUtils';

const { Title, Text, Paragraph } = Typography;
const { Step } = Steps;

interface OrderDetailsModalProps {
  visible: boolean;
  order: Order | null;
  onClose: () => void;
  onEdit?: (order: Order) => void;
  onUpdateStatus?: (orderId: number, status: string) => void;
}

const OrderDetailsModal: React.FC<OrderDetailsModalProps> = (props) => {
  const { visible, order, onClose, onEdit } = props || {};
  const { isDark } = useUserStore();
  const { t } = useTranslation('orderDetailsModal');
  const { isMobile } = useResponsive();

  // mobile responsive
  const mobileResponsive: Breakpoint[] | undefined = isMobile
    ? ['md']
    : undefined;

  // Responsive values
  const modalWidth = useResponsiveValue({
    xs: '95%',
    sm: '90%',
    md: '900px',
    lg: '1000px',
    xl: '1000px',
    '2xl': '1000px',
  });

  const avatarSize = useResponsiveValue({
    xs: 48,
    sm: 56,
    md: 64,
    lg: 64,
    xl: 64,
    '2xl': 64,
  });

  const descriptionColumns = useResponsiveValue({
    xs: 1,
    sm: 1,
    md: 2,
    lg: 2,
    xl: 2,
    '2xl': 2,
  });

  if (!order) return null;

  const getPaymentStatusColor = (status: Order['paymentStatus']) => {
    switch (status) {
      case 'completed':
        return 'green';
      case 'processing':
        return 'blue';
      case 'pending':
        return 'orange';
      case 'failed':
        return 'red';
      default:
        return 'default';
    }
  };

  const getPaymentMethodIcon = (method?: string) => {
    switch (method) {
      case 'cash':
        return <DollarOutlined />;
      case 'card':
        return <CreditCardOutlined />;
      case 'digital_wallet':
        return <BankOutlined />;
      case 'crypto':
        return <QrcodeOutlined />;
      default:
        return <DollarOutlined />;
    }
  };

  const getStatusIcon = (status: Order['status']) => {
    switch (status) {
      case 'completed':
        return <CheckCircleOutlined className='text-green-600' />;
      case 'pending':
        return <ClockCircleOutlined className='text-orange-600' />;
      case 'failed':
        return <CloseCircleOutlined className='text-red-600' />;
      case 'cancelled':
        return <ExclamationCircleOutlined className='text-gray-600' />;
      default:
        return <ClockCircleOutlined />;
    }
  };

  const getCurrentStep = () => {
    switch (order.orderStatus) {
      case 'PENDING':
        return 0;
      case 'COMPLETED':
        return 2;
      case 'REJECTED':
      case 'CANCELLED':
        return 1;
      default:
        return 0;
    }
  };

  const getStepStatus = (stepIndex: number) => {
    const currentStep = getCurrentStep();
    if (stepIndex < currentStep) return 'finish';
    if (stepIndex === currentStep) {
      if (order.orderStatus === 'REJECTED' || order.orderStatus === 'CANCELLED')
        return 'error';
      return 'process';
    }
    return 'wait';
  };

  const itemColumns = [
    {
      title: t('itemName'),
      dataIndex: 'name',
      key: 'name',
      render: (name: string, record: OrderItem) => (
        <div>
          <div
            className={`font-medium text-gray-900 ${isMobile ? 'text-sm' : ''}`}
          >
            {name}
          </div>
          {record.category && !isMobile && (
            <Tag className='mt-1'>{record.category}</Tag>
          )}
        </div>
      ),
      width: isMobile ? 120 : undefined,
    },
    {
      title: t('quantity'),
      dataIndex: 'quantity',
      key: 'quantity',
      width: isMobile ? 60 : 100,
      render: (quantity: number) => (
        <span className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
          {quantity}
        </span>
      ),
    },
    {
      title: t('unitPrice'),
      dataIndex: 'unitPrice',
      key: 'unitPrice',
      width: isMobile ? 80 : 120,
      render: (price: number) => (
        <span className={isMobile ? 'text-xs' : ''}>
          {formatCurrency(price)}
        </span>
      ),
      responsive: mobileResponsive,
    },
    {
      title: t('totalPrice'),
      dataIndex: 'totalPrice',
      key: 'totalPrice',
      width: isMobile ? 80 : 120,
      render: (total: number) => (
        <span
          className={`font-medium text-green-600 ${isMobile ? 'text-sm' : ''}`}
        >
          {formatCurrency(total)}
        </span>
      ),
    },
  ];

  const handleCopyQRCode = () => {
    if (order.qrCode) {
      navigator.clipboard.writeText(order.qrCode);
      // You could add a success message here
    }
  };

  const timelineItems = [
    {
      color: 'blue',
      children: (
        <div>
          <div className='font-medium'>{t('orderCreated')}</div>
          <div className='text-sm text-gray-500'>
            {formatDateTime(order.createdAt)}
          </div>
          {order.staffId && (
            <div className='text-xs text-gray-400'>
              {t('by')} {order.staffId}
            </div>
          )}
        </div>
      ),
    },
    ...(order.paymentStatus === 'processing'
      ? [
          {
            color: 'orange' as const,
            children: (
              <div>
                <div className='font-medium'>Payment Processing</div>
                <div className='text-sm text-gray-500'>
                  Payment is being processed
                </div>
              </div>
            ),
          },
        ]
      : []),
    ...(order.status === 'completed'
      ? [
          {
            color: 'green' as const,
            children: (
              <div>
                <div className='font-medium'>Order Completed</div>
                <div className='text-sm text-gray-500'>
                  {formatDateTime(order.updatedAt || order.createdAt)}
                </div>
              </div>
            ),
          },
        ]
      : []),
    ...(order.status === 'failed' || order.status === 'cancelled'
      ? [
          {
            color: 'red' as const,
            children: (
              <div>
                <div className='font-medium'>
                  {order.status === 'failed'
                    ? t('orderFailed')
                    : t('orderCancelled')}
                </div>
                <div className='text-sm text-gray-500'>
                  {formatDateTime(order.updatedAt || order.createdAt)}
                </div>
                {order.refundReason && (
                  <div className='text-xs text-gray-400'>
                    {t('reason')}: {order.refundReason}
                  </div>
                )}
              </div>
            ),
          },
        ]
      : []),
  ];

  return (
    <Modal
      open={visible}
      onCancel={onClose}
      footer={null}
      width={modalWidth}
      className={isMobile ? 'top-4' : 'top-8'}
      closeIcon={
        <CloseOutlined className='text-gray-400 hover:text-gray-600' />
      }
      styles={{
        body: { padding: isMobile ? '16px' : '24px' },
      }}
    >
      <div>
        {/* Header */}
        <div
          className={`flex ${isMobile ? 'flex-col gap-4' : 'items-center justify-between'} mb-6`}
        >
          <div className={`flex items-center ${isMobile ? 'gap-3' : 'gap-4'}`}>
            <Avatar
              size={avatarSize}
              className={`bg-gradient-to-br from-green-500 to-emerald-600 text-white font-bold ${isMobile ? 'text-lg' : 'text-xl'} flex-shrink-0`}
              icon={<ShoppingCartOutlined />}
            />
            <div className='min-w-0 flex-1'>
              <div
                className={`flex ${isMobile ? 'flex-col gap-1' : 'items-center gap-3'}`}
              >
                <Title
                  level={3}
                  className={`text-gray-900 mb-0 font-bold ${isMobile ? 'text-lg' : ''}`}
                >
                  {t('orderDetails')} #
                  {isMobile ? order.orderCode.slice(-6) : order.orderCode}
                </Title>
                <OrderTypeTag type={order.orderType} />
                <OrderStatusTag status={order.orderStatus} />
              </div>
              <div className='flex items-center gap-2 mt-1'>
                <Text className={`text-gray-600 ${isMobile ? 'text-sm' : ''}`}>
                  {order.notes || 'No description'}
                </Text>
              </div>
            </div>
          </div>

          <Space
            className={isMobile ? 'w-full' : ''}
            direction={isMobile ? 'vertical' : 'horizontal'}
          >
            {!isMobile && (
              <Button
                icon={<PrinterOutlined />}
                onClick={() => window.print()}
                className='hover:bg-gray-100'
              >
                {t('print')}
              </Button>
            )}
            {onEdit && (
              <Button
                type='primary'
                icon={<EditOutlined />}
                onClick={() => onEdit(order)}
                className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'w-full' : ''}`}
                size={isMobile ? 'middle' : 'large'}
              >
                {t('edit')}
              </Button>
            )}
          </Space>
        </div>

        <Divider className='my-6' />

        {/* Order Progress */}
        <Card
          className='mb-6'
          title={
            <span
              className={`flex items-center gap-2 ${isMobile ? 'text-sm' : ''}`}
            >
              <ClockCircleOutlined className='text-blue-600' />
              {t('orderProgress')}
            </span>
          }
        >
          <Steps
            current={getCurrentStep()}
            className='mb-4'
            direction={isMobile ? 'vertical' : 'horizontal'}
            size={isMobile ? 'small' : 'default'}
          >
            <Step
              title={t('orderCreated')}
              status={getStepStatus(0)}
              icon={getStatusIcon('pending')}
            />
            <Step
              title={t('paymentProcessing')}
              status={getStepStatus(1)}
              icon={getStatusIcon(
                order.paymentStatus === 'processing' ? 'pending' : order.status,
              )}
            />
            <Step
              title={t('orderCompleted')}
              status={getStepStatus(2)}
              icon={getStatusIcon(order.status)}
            />
          </Steps>
          {!isMobile && (
            <div className='pt-8 px-2'>
              <Timeline items={timelineItems} />
            </div>
          )}
        </Card>

        {/* Order Information */}
        <Row
          gutter={[
            { xs: 12, sm: 16, lg: 24 },
            { xs: 12, sm: 16, lg: 24 },
          ]}
          className='mb-6'
        >
          <Col xs={24} lg={16}>
            <Card
              title={
                <span
                  className={`flex items-center gap-2 ${isMobile ? 'text-sm' : ''}`}
                >
                  <FileTextOutlined className='text-blue-600' />
                  {t('orderInformation')}
                </span>
              }
            >
              <Descriptions
                column={descriptionColumns}
                size={isMobile ? 'small' : 'middle'}
              >
                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <UserOutlined className='text-gray-500' />
                      {t('member')}
                    </span>
                  }
                >
                  <span className='font-medium'>User {order.userId.slice(0, 8)}...</span>
                  <div className='text-sm text-gray-500 !ml-2'>
                    ID:{' '}
                    <span className={isDark ? 'text-white' : 'text-white'}>
                      {order.userId}
                    </span>
                  </div>
                </Descriptions.Item>

                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <DollarOutlined className='text-gray-500' />
                      {t('amount')}
                    </span>
                  }
                >
                  <span className='font-bold text-green-600 text-lg'>
                    {formatOrderAmount(order.quantity * order.pricePerUnit, order.currencyUnit)}
                  </span>
                </Descriptions.Item>

                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <CalendarOutlined className='text-gray-500' />
                      {t('date')}
                    </span>
                  }
                >
                  {formatDateTime(order.createdAt)}
                </Descriptions.Item>

                <Descriptions.Item
                  label={
                    <span className='flex items-center gap-2'>
                      <TagOutlined className='text-gray-500' />
                      {t('paymentStatus')}
                    </span>
                  }
                >
                  <Tag
                    color={getPaymentStatusColor(order.paymentStatus)}
                    className='font-medium'
                  >
                    {t(order.paymentStatus)}
                  </Tag>
                </Descriptions.Item>

                {order.paymentMethod && (
                  <Descriptions.Item
                    label={
                      <span className='flex items-center gap-2'>
                        {getPaymentMethodIcon(order.paymentMethod)}
                        {t('paymentMethod')}
                      </span>
                    }
                  >
                    {t(order.paymentMethod)}
                  </Descriptions.Item>
                )}

                {order.transactionId && (
                  <Descriptions.Item
                    label={
                      <span className='flex items-center gap-2'>
                        <BankOutlined className='text-gray-500' />
                        {t('transactionId')}
                      </span>
                    }
                  >
                    <span
                      className={`${isDark ? 'bg-slate-600' : 'bg-slate-100'} font-mono px-2 py-1 rounded text-sm`}
                    >
                      {order.transactionId}
                    </span>
                  </Descriptions.Item>
                )}
              </Descriptions>

              {order.notes && (
                <div className='mt-4'>
                  <div className='flex items-center gap-2 mb-2'>
                    <FileTextOutlined className='text-gray-500' />
                    <Text className='font-medium text-gray-700'>
                      {t('notes')}
                    </Text>
                  </div>
                  <Paragraph className='text-gray-600 ml-6 mb-0'>
                    {order.notes}
                  </Paragraph>
                </div>
              )}
            </Card>
          </Col>
          <Col xs={24} lg={8}>
            {/* QR Code */}
            {order.qrCode && (
              <Card
                title={
                  <span
                    className={`flex items-center gap-2 ${isMobile ? 'text-sm' : ''}`}
                  >
                    <QrcodeOutlined className='text-blue-600' />
                    {t('qrCode')}
                  </span>
                }
                className='mb-4'
              >
                <div className='text-center flex flex-col items-center'>
                  <QRCode
                    value={order.qrCode}
                    className='mb-4'
                    size={isMobile ? 120 : 160}
                  />
                  <div
                    className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-600 mb-3`}
                  >
                    {isMobile ? order.qrCode.slice(-8) : order.qrCode}
                  </div>
                  <Button
                    icon={<CopyOutlined />}
                    onClick={handleCopyQRCode}
                    size={isMobile ? 'small' : 'small'}
                    className='w-full'
                  >
                    {t('copyQrCode')}
                  </Button>
                </div>
              </Card>
            )}
          </Col>
        </Row>
        <Row gutter={[24, 24]} className='mb-6'>
          <Col xs={24} lg={24}>
            {/* Refund Information */}
            {order.refundAmount && (
              <Alert
                message={t('refundProcessed')}
                description={
                  <div>
                    <div className='font-medium'>
                      {formatCurrency(order.refundAmount)}
                    </div>
                    {order.refundReason && (
                      <div className='text-sm mt-1'>
                        {t('reason')}: {order.refundReason}
                      </div>
                    )}
                  </div>
                }
                type='info'
                showIcon
                className='mb-4'
              />
            )}
          </Col>
        </Row>
        {/* Order Items */}
        {order.items && order.items.length > 0 && (
          <Card
            title={
              <span
                className={`flex items-center gap-2 ${isMobile ? 'text-sm' : ''}`}
              >
                <ShoppingCartOutlined className='text-blue-600' />
                {t('orderItems')}
              </span>
            }
            extra={
              !isMobile && (
                <Text className='text-gray-500'>
                  {order.items.length}{' '}
                  {order.items.length === 1 ? t('item') : t('items')}
                </Text>
              )
            }
          >
            <Table
              dataSource={order.items}
              columns={itemColumns}
              pagination={false}
              rowKey='id'
              size={isMobile ? 'small' : 'large'}
              scroll={isMobile ? { x: 400 } : undefined}
              summary={() => (
                <Table.Summary.Row>
                  <Table.Summary.Cell index={0} colSpan={3}>
                    <Text className='font-medium'>{t('total')}</Text>
                  </Table.Summary.Cell>
                  <Table.Summary.Cell index={1}>
                    <Text className='font-bold text-green-600 text-lg'>
                      {formatOrderAmount(order.quantity * order.pricePerUnit, order.currencyUnit)}
                    </Text>
                  </Table.Summary.Cell>
                </Table.Summary.Row>
              )}
            />
          </Card>
        )}
      </div>
    </Modal>
  );
};

export default OrderDetailsModal;
