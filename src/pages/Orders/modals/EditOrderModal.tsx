import React, { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Space,
  Typography,
  Divider,
  Row,
  Col,
  Card,
} from 'antd';
import {
  EditOutlined,
  DollarOutlined,
  NumberOutlined,
  FileTextOutlined,
} from '@ant-design/icons';
import { useEditOrder } from '@/hooks/useOrders';
import type { Order } from '@/types';
import { OrderStatusEnums, CurrencyCodeEnums } from '@/utils/enums';
import { formatOrderAmount } from '@/utils/currencyUtils';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

interface EditOrderModalProps {
  visible: boolean;
  onCancel: () => void;
  order: Order | null;
}

const EditOrderModal: React.FC<EditOrderModalProps> = ({
  visible,
  onCancel,
  order,
}) => {
  const [form] = Form.useForm();
  const editOrderMutation = useEditOrder();

  useEffect(() => {
    if (visible && order) {
      form.setFieldsValue({
        quantity: order.quantity,
        priceOfUnit: order.pricePerUnit,
        currencyUnit: order.currencyUnit,
        orderStatus: order.orderStatus,
        notes: order.notes,
        metadata: order.metadata ? JSON.stringify(order.metadata, null, 2) : '',
      });
    }
  }, [visible, order, form]);

  const handleSubmit = async () => {
    if (!order) return;

    try {
      const values = await form.validateFields();
      
      let metadata = null;
      if (values.metadata && values.metadata.trim()) {
        try {
          metadata = JSON.parse(values.metadata);
        } catch (e) {
          // If JSON parsing fails, store as string
          metadata = values.metadata;
        }
      }

      await editOrderMutation.mutateAsync({
        orderId: order.orderId,
        orderData: {
          orderId: order.orderId,
          quantity: values.quantity,
          priceOfUnit: values.priceOfUnit,
          currencyUnit: values.currencyUnit,
          orderStatus: values.orderStatus,
          notes: values.notes || '',
          metadata,
        },
      });

      onCancel();
      form.resetFields();
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const handleCancel = () => {
    onCancel();
    form.resetFields();
  };

  // Calculate total price in real-time
  const quantity = Form.useWatch('quantity', form) || 0;
  const priceOfUnit = Form.useWatch('priceOfUnit', form) || 0;
  const currencyUnit = Form.useWatch('currencyUnit', form) || 'USD';
  const totalPrice = quantity * priceOfUnit;

  return (
    <Modal
      title={
        <div className="flex items-center gap-3">
          <div className="p-2 bg-blue-100 rounded-lg">
            <EditOutlined className="text-blue-600 text-lg" />
          </div>
          <div>
            <Title level={4} className="!mb-0">
              Edit Order
            </Title>
            <Text className="text-gray-500">
              Order #{order?.orderCode?.slice(0, 8)}...
            </Text>
          </div>
        </div>
      }
      open={visible}
      onCancel={handleCancel}
      width={800}
      footer={null}
      className="edit-order-modal"
    >
      <div className="space-y-6">
        {/* Order Summary Card */}
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <Row gutter={16}>
            <Col span={8}>
              <div className="text-center">
                <Text className="text-gray-600 block">Order ID</Text>
                <Text className="text-lg font-semibold">{order?.orderId}</Text>
              </div>
            </Col>
            <Col span={8}>
              <div className="text-center">
                <Text className="text-gray-600 block">Current Total</Text>
                <Text className="text-lg font-semibold text-green-600">
                  {order && formatOrderAmount(order.quantity * order.pricePerUnit, order.currencyUnit)}
                </Text>
              </div>
            </Col>
            <Col span={8}>
              <div className="text-center">
                <Text className="text-gray-600 block">New Total</Text>
                <Text className="text-lg font-semibold text-blue-600">
                  {formatOrderAmount(totalPrice, currencyUnit)}
                </Text>
              </div>
            </Col>
          </Row>
        </Card>

        <Form
          form={form}
          layout="vertical"
          requiredMark={false}
          className="space-y-4"
        >
          <Row gutter={16}>
            {/* Quantity */}
            <Col span={12}>
              <Form.Item
                name="quantity"
                label={
                  <div className="flex items-center gap-2">
                    <NumberOutlined className="text-blue-500" />
                    <span className="font-medium">Quantity</span>
                  </div>
                }
                rules={[
                  { required: true, message: 'Please enter quantity' },
                  { type: 'number', min: 1, message: 'Quantity must be at least 1' },
                ]}
              >
                <InputNumber
                  placeholder="Enter quantity"
                  min={1}
                  className="w-full"
                  size="large"
                />
              </Form.Item>
            </Col>

            {/* Price Per Unit */}
            <Col span={12}>
              <Form.Item
                name="priceOfUnit"
                label={
                  <div className="flex items-center gap-2">
                    <DollarOutlined className="text-green-500" />
                    <span className="font-medium">Price Per Unit</span>
                  </div>
                }
                rules={[
                  { required: true, message: 'Please enter price per unit' },
                  { type: 'number', min: 0.01, message: 'Price must be greater than 0' },
                ]}
              >
                <InputNumber
                  placeholder="Enter price per unit"
                  min={0.01}
                  step={0.01}
                  precision={2}
                  className="w-full"
                  size="large"
                />
              </Form.Item>
            </Col>
          </Row>

          <Row gutter={16}>
            {/* Currency */}
            <Col span={12}>
              <Form.Item
                name="currencyUnit"
                label={
                  <span className="font-medium">Currency</span>
                }
                rules={[{ required: true, message: 'Please select currency' }]}
              >
                <Select
                  placeholder="Select currency"
                  size="large"
                  className="w-full"
                >
                  {Object.values(CurrencyCodeEnums).map(currency => (
                    <Option key={currency} value={currency}>
                      <div className="flex items-center gap-2">
                        <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                          {currency}
                        </span>
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>

            {/* Order Status */}
            <Col span={12}>
              <Form.Item
                name="orderStatus"
                label={
                  <span className="font-medium">Order Status</span>
                }
                rules={[{ required: true, message: 'Please select order status' }]}
              >
                <Select
                  placeholder="Select order status"
                  size="large"
                  className="w-full"
                >
                  {Object.values(OrderStatusEnums).map(status => (
                    <Option key={status} value={status}>
                      <div className="flex items-center gap-2">
                        <div className={`w-2 h-2 rounded-full ${
                          status === 'COMPLETED' ? 'bg-green-400' :
                          status === 'PROCESSING' ? 'bg-blue-400' :
                          status === 'PENDING' ? 'bg-orange-400' :
                          status === 'CONFIRMED' ? 'bg-purple-400' :
                          status === 'CANCELLED' ? 'bg-gray-400' :
                          'bg-red-400'
                        }`}></div>
                        {status}
                      </div>
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>

          {/* Notes */}
          <Form.Item
            name="notes"
            label={
              <div className="flex items-center gap-2">
                <FileTextOutlined className="text-gray-500" />
                <span className="font-medium">Notes</span>
              </div>
            }
          >
            <TextArea
              placeholder="Enter order notes..."
              rows={3}
              className="w-full"
            />
          </Form.Item>

          {/* Metadata */}
          <Form.Item
            name="metadata"
            label={
              <span className="font-medium">Metadata (JSON)</span>
            }
          >
            <TextArea
              placeholder='{"key": "value"}'
              rows={4}
              className="w-full font-mono text-sm"
            />
          </Form.Item>
        </Form>

        <Divider />

        {/* Footer Actions */}
        <div className="flex justify-end gap-3">
          <Button
            size="large"
            onClick={handleCancel}
            className="px-6"
          >
            Cancel
          </Button>
          <Button
            type="primary"
            size="large"
            onClick={handleSubmit}
            loading={editOrderMutation.isPending}
            className="px-6 bg-blue-600 hover:bg-blue-700"
          >
            Update Order
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default EditOrderModal;
