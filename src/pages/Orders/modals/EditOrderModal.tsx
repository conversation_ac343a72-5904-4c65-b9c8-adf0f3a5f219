import React, { useEffect } from 'react';
import {
  Modal,
  Form,
  Input,
  InputNumber,
  Select,
  Button,
  Typography,
  Row,
  Col,
  Card,
  Space,
  Tag,
} from 'antd';
import {
  EditOutlined,
  DollarOutlined,
  NumberOutlined,
  FileTextOutlined,
  UserOutlined,
  BankOutlined,
  CalendarOutlined,
  SaveOutlined,
  CloseOutlined,
} from '@ant-design/icons';
import { useEditOrder } from '@/hooks/useOrders';
import type { Order } from '@/types';
import { OrderStatusEnums, CurrencyCodeEnums } from '@/utils/enums';
import { formatOrderAmount, getCurrencySymbol } from '@/utils/currencyUtils';
import { OrderStatusTag, OrderTypeTag, CurrencyTag } from '@/components';

const { Option } = Select;
const { TextArea } = Input;
const { Title, Text } = Typography;

interface EditOrderModalProps {
  visible: boolean;
  onCancel: () => void;
  order: Order | null;
}

const EditOrderModal: React.FC<EditOrderModalProps> = ({
  visible,
  onCancel,
  order,
}) => {
  const [form] = Form.useForm();
  const editOrderMutation = useEditOrder();

  useEffect(() => {
    if (visible && order) {
      form.setFieldsValue({
        quantity: order.quantity,
        priceOfUnit: order.pricePerUnit,
        currencyUnit: order.currencyUnit,
        orderStatus: order.orderStatus,
        notes: order.notes,
        metadata: order.metadata ? JSON.stringify(order.metadata, null, 2) : '',
      });
    }
  }, [visible, order, form]);

  const handleSubmit = async () => {
    if (!order) return;

    try {
      const values = await form.validateFields();
      
      let metadata = null;
      if (values.metadata && values.metadata.trim()) {
        try {
          metadata = JSON.parse(values.metadata);
        } catch (e) {
          // If JSON parsing fails, store as string
          metadata = values.metadata;
        }
      }

      await editOrderMutation.mutateAsync({
        orderId: order.orderId,
        orderData: {
          orderId: order.orderId,
          quantity: values.quantity,
          priceOfUnit: values.priceOfUnit,
          currencyUnit: values.currencyUnit,
          orderStatus: values.orderStatus,
          notes: values.notes || '',
          metadata,
        },
      });

      onCancel();
      form.resetFields();
    } catch (error) {
      console.error('Form validation failed:', error);
    }
  };

  const handleCancel = () => {
    onCancel();
    form.resetFields();
  };

  // Calculate total price in real-time
  const quantity = Form.useWatch('quantity', form) || 0;
  const priceOfUnit = Form.useWatch('priceOfUnit', form) || 0;
  const currencyUnit = Form.useWatch('currencyUnit', form) || 'USD';
  const totalPrice = quantity * priceOfUnit;

  return (
    <Modal
      title={null}
      open={visible}
      onCancel={handleCancel}
      width={900}
      footer={null}
      className="edit-order-modal"
      styles={{
        body: { padding: 0 },
        header: { display: 'none' }
      }}
    >
      <div className="bg-white">
        {/* Header */}
        <div className="bg-gradient-to-r from-blue-600 to-indigo-600 px-8 py-6 text-white">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="p-3 bg-white/20 rounded-xl backdrop-blur-sm">
                <EditOutlined className="text-white text-2xl" />
              </div>
              <div>
                <Title level={3} className="!text-white !mb-1">
                  Edit Order
                </Title>
                <div className="flex items-center gap-3">
                  <Text className="text-blue-100">
                    #{order?.orderCode?.slice(0, 8)}...
                  </Text>
                  <OrderTypeTag type={order?.orderType || 'BUY'} />
                  <OrderStatusTag status={order?.orderStatus || 'PENDING'} />
                </div>
              </div>
            </div>
            <Button
              type="text"
              icon={<CloseOutlined />}
              onClick={handleCancel}
              className="text-white hover:bg-white/20 border-0"
              size="large"
            />
          </div>
        </div>

        {/* Order Info Cards */}
        <div className="px-8 py-6 bg-gray-50">
          <Row gutter={16}>
            <Col span={6}>
              <Card className="text-center border-0 shadow-sm">
                <div className="flex flex-col items-center gap-2">
                  <div className="p-2 bg-blue-100 rounded-lg">
                    <NumberOutlined className="text-blue-600" />
                  </div>
                  <Text className="text-gray-500 text-sm">Order ID</Text>
                  <Text className="font-semibold">{order?.orderId}</Text>
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card className="text-center border-0 shadow-sm">
                <div className="flex flex-col items-center gap-2">
                  <div className="p-2 bg-green-100 rounded-lg">
                    <UserOutlined className="text-green-600" />
                  </div>
                  <Text className="text-gray-500 text-sm">User ID</Text>
                  <Text className="font-semibold">{order?.userId?.slice(0, 8)}...</Text>
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card className="text-center border-0 shadow-sm">
                <div className="flex flex-col items-center gap-2">
                  <div className="p-2 bg-purple-100 rounded-lg">
                    <BankOutlined className="text-purple-600" />
                  </div>
                  <Text className="text-gray-500 text-sm">Counter</Text>
                  <Text className="font-semibold">{order?.counterId}</Text>
                </div>
              </Card>
            </Col>
            <Col span={6}>
              <Card className="text-center border-0 shadow-sm">
                <div className="flex flex-col items-center gap-2">
                  <div className="p-2 bg-orange-100 rounded-lg">
                    <CalendarOutlined className="text-orange-600" />
                  </div>
                  <Text className="text-gray-500 text-sm">Created</Text>
                  <Text className="font-semibold">
                    {order?.createdAt ? new Date(order.createdAt).toLocaleDateString() : '-'}
                  </Text>
                </div>
              </Card>
            </Col>
          </Row>
        </div>

        {/* Price Comparison */}
        <div className="px-8 py-4 ">
          <Row gutter={24}>
            <Col span={12}>
              <div className="text-center p-4 bg-white rounded-xl shadow-sm">
                <Text className="text-gray-500 block mb-2">Current Total</Text>
                <Text className="text-2xl font-bold text-green-600">
                  {order && formatOrderAmount(order.quantity * order.pricePerUnit, order.currencyUnit)}
                </Text>
                <Text className="text-sm text-gray-500 block mt-1">
                  {order?.quantity} × {order && formatOrderAmount(order.pricePerUnit, order.currencyUnit)}
                </Text>
              </div>
            </Col>
            <Col span={12}>
              <div className="text-center p-4 bg-white rounded-xl shadow-sm">
                <Text className="text-gray-500 block mb-2">New Total</Text>
                <Text className="text-2xl font-bold text-blue-600">
                  {formatOrderAmount(totalPrice, currencyUnit)}
                </Text>
                <Text className="text-sm text-gray-500 block mt-1">
                  {quantity} × {formatOrderAmount(priceOfUnit, currencyUnit)}
                </Text>
              </div>
            </Col>
          </Row>
        </div>

        {/* Form Section */}
        <div className="px-8 py-6">
          <Form
            form={form}
            layout="vertical"
            requiredMark={false}
            className="space-y-6"
          >
            {/* Main Fields */}
            <Card title="Order Details" className="border-0 shadow-sm">
              <Row gutter={24}>
                {/* Quantity */}
                <Col span={12}>
                  <Form.Item
                    name="quantity"
                    label={
                      <div className="flex items-center gap-2 mb-2">
                        <div className="p-1 bg-blue-100 rounded">
                          <NumberOutlined className="text-blue-600 text-sm" />
                        </div>
                        <span className="font-medium text-gray-700">Quantity</span>
                      </div>
                    }
                    rules={[
                      { required: true, message: 'Please enter quantity' },
                      { type: 'number', min: 1, message: 'Quantity must be at least 1' },
                    ]}
                  >
                    <InputNumber
                      placeholder="Enter quantity"
                      min={1}
                      className="w-full"
                      size="large"
                      style={{ borderRadius: '8px' }}
                    />
                  </Form.Item>
                </Col>

                {/* Price Per Unit */}
                <Col span={12}>
                  <Form.Item
                    name="priceOfUnit"
                    label={
                      <div className="flex items-center gap-2 mb-2">
                        <div className="p-1 bg-green-100 rounded">
                          <DollarOutlined className="text-green-600 text-sm" />
                        </div>
                        <span className="font-medium text-gray-700">Price Per Unit</span>
                      </div>
                    }
                    rules={[
                      { required: true, message: 'Please enter price per unit' },
                      { type: 'number', min: 0.01, message: 'Price must be greater than 0' },
                    ]}
                  >
                    <InputNumber
                      placeholder="Enter price per unit"
                      min={0.01}
                      step={0.01}
                      precision={2}
                      className="w-full"
                      size="large"
                      style={{ borderRadius: '8px' }}
                      addonBefore={getCurrencySymbol(currencyUnit)}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Currency & Status */}
            <Card title="Settings" className="border-0 shadow-sm">
              <Row gutter={24}>
                {/* Currency */}
                <Col span={12}>
                  <Form.Item
                    name="currencyUnit"
                    label={
                      <div className="flex items-center gap-2 mb-2">
                        <div className="p-1 bg-yellow-100 rounded">
                          <DollarOutlined className="text-yellow-600 text-sm" />
                        </div>
                        <span className="font-medium text-gray-700">Currency</span>
                      </div>
                    }
                    rules={[{ required: true, message: 'Please select currency' }]}
                  >
                    <Select
                      placeholder="Select currency"
                      size="large"
                      className="w-full"
                      style={{ borderRadius: '8px' }}
                    >
                      {Object.values(CurrencyCodeEnums).map(currency => (
                        <Option key={currency} value={currency}>
                          <div className="flex items-center gap-2">
                            <span className="font-mono text-xs bg-gray-100 px-2 py-1 rounded">
                              {getCurrencySymbol(currency)}
                            </span>
                            <span>{currency}</span>
                          </div>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>

                {/* Order Status */}
                <Col span={12}>
                  <Form.Item
                    name="orderStatus"
                    label={
                      <div className="flex items-center gap-2 mb-2">
                        <div className="p-1 bg-purple-100 rounded">
                          <EditOutlined className="text-purple-600 text-sm" />
                        </div>
                        <span className="font-medium text-gray-700">Order Status</span>
                      </div>
                    }
                    rules={[{ required: true, message: 'Please select order status' }]}
                  >
                    <Select
                      placeholder="Select order status"
                      size="large"
                      className="w-full"
                      style={{ borderRadius: '8px' }}
                    >
                      {Object.values(OrderStatusEnums).map(status => (
                        <Option key={status} value={status}>
                          <div className="flex items-center gap-2">
                            <div className={`w-2 h-2 rounded-full ${
                              status === 'COMPLETED' ? 'bg-green-400' :
                              status === 'PROCESSING' ? 'bg-blue-400' :
                              status === 'PENDING' ? 'bg-orange-400' :
                              status === 'CONFIRMED' ? 'bg-purple-400' :
                              status === 'CANCELLED' ? 'bg-gray-400' :
                              'bg-red-400'
                            }`}></div>
                            {status}
                          </div>
                        </Option>
                      ))}
                    </Select>
                  </Form.Item>
                </Col>
              </Row>
            </Card>

            {/* Notes & Metadata */}
            <Card title="Additional Information" className="border-0 shadow-sm">
              <Row gutter={24}>
                <Col span={12}>
                  <Form.Item
                    name="notes"
                    label={
                      <div className="flex items-center gap-2 mb-2">
                        <div className="p-1 bg-gray-100 rounded">
                          <FileTextOutlined className="text-gray-600 text-sm" />
                        </div>
                        <span className="font-medium text-gray-700">Notes</span>
                      </div>
                    }
                  >
                    <TextArea
                      placeholder="Enter order notes..."
                      rows={4}
                      className="w-full"
                      style={{ borderRadius: '8px' }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name="metadata"
                    label={
                      <div className="flex items-center gap-2 mb-2">
                        <div className="p-1 bg-indigo-100 rounded">
                          <FileTextOutlined className="text-indigo-600 text-sm" />
                        </div>
                        <span className="font-medium text-gray-700">Metadata (JSON)</span>
                      </div>
                    }
                  >
                    <TextArea
                      placeholder='{"key": "value"}'
                      rows={4}
                      className="w-full font-mono text-sm"
                      style={{ borderRadius: '8px' }}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Card>
          </Form>
        </div>

        {/* Footer Actions */}
        <div className="px-8 py-6 bg-gray-50 border-t flex justify-end gap-4">
          <Button
            size="large"
            onClick={handleCancel}
            className="px-8 h-12 rounded-lg"
          >
            Cancel
          </Button>
          <Button
            type="primary"
            size="large"
            onClick={handleSubmit}
            loading={editOrderMutation.isPending}
            icon={<SaveOutlined />}
            className="px-8 h-12 bg-blue-600 hover:bg-blue-700 rounded-lg"
          >
            Update Order
          </Button>
        </div>
      </div>
    </Modal>
  );
};

export default EditOrderModal;
