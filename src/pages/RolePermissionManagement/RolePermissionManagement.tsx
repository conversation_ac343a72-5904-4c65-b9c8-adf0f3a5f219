import {
  KeyOutlined,
  PlusOutlined,
  SafetyOutlined,
  UserOutlined,
} from '@ant-design/icons';
import { <PERSON>ton, Card, Space, Tabs, Typography } from 'antd';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useResponsive } from '@/hooks/useResponsive.ts';
import {
  usePermissionCategories,
  usePermissions,
  useRoles,
  useUserRoles,
} from '@/hooks/useRoles.ts';
import AssignRoleModal from '@/pages/RolePermissionManagement/components/modals/AssignRoleModal';
import CreateRoleModal from '@/pages/RolePermissionManagement/components/modals/CreateRoleModal';
import EditRoleModal from '@/pages/RolePermissionManagement/components/modals/EditRoleModal';
import PermissionsList from '@/pages/RolePermissionManagement/components/PermissionsList';
import RolesList from '@/pages/RolePermissionManagement/components/RolesList';
import UserRolesList from '@/pages/RolePermissionManagement/components/UserRolesList';
import type { Role } from '@/types';

const { Title, Text } = Typography;

const RolePermissionManagement: React.FC = () => {
  const { t } = useTranslation('rolePermissionManagement');
  const { isMobile } = useResponsive();
  const [activeTab, setActiveTab] = useState('roles');
  const [searchTerm] = useState('');
  const [statusFilter] = useState('');
  const [roleFilter] = useState('');
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [showAssignModal, setShowAssignModal] = useState(false);
  const [selectedRole, setSelectedRole] = useState<Role | null>(null);
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: isMobile ? 5 : 10,
  });

  // Fetch data
  const { data: rolesResponse, isLoading: rolesLoading } = useRoles({
    page: pagination.current,
    pageSize: pagination.pageSize,
    search: searchTerm,
    status: statusFilter,
  });

  const { data: permissionsResponse, isLoading: permissionsLoading } =
    usePermissions();
  const { data: categoriesResponse, isLoading: categoriesLoading } =
    usePermissionCategories();
  const { data: userRolesResponse, isLoading: userRolesLoading } = useUserRoles(
    {
      page: pagination.current,
      pageSize: pagination.pageSize,
      search: searchTerm,
      roleId: roleFilter,
    },
  );

  const roles = rolesResponse?.data?.data || [];
  const permissions = permissionsResponse?.data || [];
  const categories = categoriesResponse?.data || [];
  const userRoles = userRolesResponse?.data?.data || [];
  const totalRoles = rolesResponse?.data?.total || 0;
  const totalUserRoles = userRolesResponse?.data?.total || 0;

  const handleCreateRole = () => {
    setShowCreateModal(true);
  };

  const handleEditRole = (role: Role) => {
    setSelectedRole(role);
    setShowEditModal(true);
  };

  const handleAssignRole = () => {
    setShowAssignModal(true);
  };

  const handlePaginationChange = (page: number, pageSize: number) => {
    setPagination({ current: page, pageSize });
  };

  const tabItems = [
    {
      key: 'roles',
      label: (
        <span className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
          <SafetyOutlined />
          <span className={isMobile ? 'text-sm' : ''}>{t('roles')}</span>
        </span>
      ),
      children: (
        <RolesList
          roles={roles}
          loading={rolesLoading}
          onEdit={handleEditRole}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: totalRoles,
            onChange: handlePaginationChange,
          }}
        />
      ),
    },
    {
      key: 'permissions',
      label: (
        <span className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
          <KeyOutlined />
          <span className={isMobile ? 'text-sm' : ''}>{t('permissions')} </span>
        </span>
      ),
      children: (
        <PermissionsList
          permissions={permissions}
          categories={categories}
          loading={permissionsLoading || categoriesLoading}
        />
      ),
    },
    {
      key: 'assignments',
      label: (
        <span className={`flex items-center ${isMobile ? 'gap-1' : 'gap-2'}`}>
          <UserOutlined />
          <span className={isMobile ? 'text-sm' : ''}>
            {isMobile ? t('assignments') : t('userAssignments')}
          </span>
        </span>
      ),
      children: (
        <UserRolesList
          userRoles={userRoles}
          roles={roles}
          loading={userRolesLoading}
          pagination={{
            current: pagination.current,
            pageSize: pagination.pageSize,
            total: totalUserRoles,
            onChange: handlePaginationChange,
          }}
        />
      ),
    },
  ];

  return (
    <div className='space-y-6'>
      {/* Header */}
      <div
        className={`flex ${isMobile ? 'flex-col' : 'flex-col sm:flex-row'} ${isMobile ? 'gap-3' : 'gap-4'} items-start sm:items-center justify-between`}
      >
        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-3'}`}>
          <SafetyOutlined
            className={`text-blue-600 ${isMobile ? 'text-xl' : 'text-2xl'}`}
          />
          <div>
            <Title
              level={2}
              className={`text-gray-900 mb-0 ${isMobile ? 'text-lg' : ''}`}
            >
              {isMobile ? t('permissMb') : t('title')}
            </Title>
            <Text className={`text-gray-500 ${isMobile ? 'text-sm' : ''}`}>
              {isMobile ? t('manaAsset') : t('subtitle')}
            </Text>
          </div>
        </div>
        <Space
          direction={isMobile ? 'horizontal' : 'horizontal'}
          className={isMobile ? 'w-full' : ''}
        >
          {activeTab === 'roles' && (
            <Button
              type='primary'
              icon={<PlusOutlined />}
              onClick={handleCreateRole}
              size={isMobile ? 'middle' : 'large'}
              className={`bg-blue-600 hover:bg-blue-700 ${isMobile ? 'flex-1' : ''}`}
            >
              {isMobile ? t('create') : t('createRole')}
            </Button>
          )}
          {activeTab === 'assignments' && (
            <Button
              type='primary'
              icon={<UserOutlined />}
              onClick={handleAssignRole}
              size={isMobile ? 'middle' : 'large'}
              className={`bg-green-600 hover:bg-green-700 ${isMobile ? 'flex-1' : ''}`}
            >
              {isMobile ? t('assign') : t('assignRole')}
            </Button>
          )}
        </Space>
      </div>

      {/* Filters */}
      {/*<Card className="rounded-2xl border-0 shadow-card">*/}
      {/*  <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">*/}
      {/*    <div className="flex flex-col sm:flex-row gap-4 flex-1">*/}
      {/*      <Search*/}
      {/*        placeholder={*/}
      {/*          activeTab === 'roles'*/}
      {/*            ? t('rolePermission.searchRoles')*/}
      {/*            : activeTab === 'permissions'*/}
      {/*            ? t('rolePermission.searchPermissions')*/}
      {/*            : t('rolePermission.searchUsers')*/}
      {/*        }*/}
      {/*        allowClear*/}
      {/*        onSearch={handleSearch}*/}
      {/*        className="sm:w-80"*/}
      {/*        size="large"*/}
      {/*      />*/}

      {/*      <Space wrap>*/}
      {/*        {(activeTab === 'roles' || activeTab === 'assignments') && (*/}
      {/*          <Select*/}
      {/*            placeholder={t('rolePermission.filterByStatus')}*/}
      {/*            allowClear*/}
      {/*            value={statusFilter || undefined}*/}
      {/*            onChange={handleStatusFilter}*/}
      {/*            className="w-40"*/}
      {/*            size="large"*/}
      {/*          >*/}
      {/*            <Option value="active">{t('rolePermission.active')}</Option>*/}
      {/*            <Option value="inactive">{t('rolePermission.inactive')}</Option>*/}
      {/*          </Select>*/}
      {/*        )}*/}
      {/*        */}
      {/*        {activeTab === 'assignments' && (*/}
      {/*          <Select*/}
      {/*            placeholder={t('rolePermission.filterByRole')}*/}
      {/*            allowClear*/}
      {/*            value={roleFilter || undefined}*/}
      {/*            onChange={handleRoleFilter}*/}
      {/*            className="w-48"*/}
      {/*            size="large"*/}
      {/*            showSearch*/}
      {/*            optionFilterProp="children"*/}
      {/*          >*/}
      {/*            {roles.map(role => (*/}
      {/*              <Option key={role.id} value={role.id}>*/}
      {/*                {role.name}*/}
      {/*              </Option>*/}
      {/*            ))}*/}
      {/*          </Select>*/}
      {/*        )}*/}
      {/*      </Space>*/}
      {/*    </div>*/}
      {/*  </div>*/}
      {/*</Card>*/}

      {/* Main Content */}
      <Card
        className={`${isMobile ? 'rounded-xl' : 'rounded-2xl'} border-0 shadow-card`}
      >
        <Tabs
          activeKey={activeTab}
          onChange={setActiveTab}
          items={tabItems}
          size={isMobile ? 'middle' : 'large'}
          className='role-permission-tabs'
          tabPosition={isMobile ? 'top' : 'top'}
        />
      </Card>

      {/* Modals */}
      <CreateRoleModal
        visible={showCreateModal}
        onClose={() => setShowCreateModal(false)}
        permissions={permissions}
        categories={categories}
      />

      <EditRoleModal
        visible={showEditModal}
        role={selectedRole}
        onClose={() => {
          setShowEditModal(false);
          setSelectedRole(null);
        }}
        permissions={permissions}
        categories={categories}
      />

      <AssignRoleModal
        visible={showAssignModal}
        onClose={() => setShowAssignModal(false)}
        roles={roles.filter((role) => role.status === 'active')}
      />
    </div>
  );
};

export default RolePermissionManagement;
