import { mockDashboardStats, mockDelay } from '@/api/mockData.ts';
import type { DashboardStats, ApiResponse } from '@/types';

// Mock API functions - replace with real API calls later
export const fetchDashboardStats = async (): Promise<
  ApiResponse<DashboardStats>
> => {
  // Simulate API delay
  await mockDelay();

  // For now, return mock data
  // Later replace with: return api.get<DashboardStats>('/dashboard/stats');
  return {
    data: mockDashboardStats,
    success: true,
    message: 'Dashboard stats fetched successfully',
  };
};
