enum WebLanguageGalaxyEnum {
  Zh_TW = 'zh-TW',
  En_US = 'en-US',
}

// Updated to match backend API values
enum OrderTypeEnums {
  BUY = 'BUY',
  SELL = 'SELL',
}

// Backend API order status values
enum OrderStatusEnums {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  PROCESSING = 'PROCESSING',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  REJECTED = 'REJECTED',
}

// Backend API currency codes
enum CurrencyCodeEnums {
  TWD = 'TWD', // New Taiwan Dollar
  HKD = 'HKD', // Hong Kong Dollar
  JPY = 'JPY', // Japanese Yen
  CNY = 'CNY', // Chinese Yuan
  SGD = 'SGD', // Singapore Dollar
  MYR = 'MYR', // Malaysian Ringgit
  KRW = 'KRW', // South Korean Won
  THB = 'THB', // Thai Baht
  USD = 'USD', // US Dollar
  GBP = 'GBP', // British Pound
  VND = 'VND', // Vietnamese Don
  PHP = 'PHP', // Philippine Peso
}

export { WebLanguageGalaxyEnum, OrderTypeEnums, OrderStatusEnums, CurrencyCodeEnums };
