import { OrderTypeEnums, OrderStatusEnums, CurrencyCodeEnums } from './enums';

type EnumObjBase<
  T extends Record<string, string | number>,
  Other = object,
> = Record<keyof T, { label: string; value: ValueOf<T> } & Other>;

const eNumEntities = <T extends object>(originEnum: T) => {
  const keyofEnums = Object.keys(originEnum);
  const keysLength = keyofEnums.length;
  const lengthAvg = Math.floor(keysLength / 2);
  const vailedKeys = keyofEnums.slice(-lengthAvg);

  const result = vailedKeys.reduce(
    (arr, key) => {
      const enumKey = key as keyof typeof originEnum;
      const valueOfObj = originEnum[enumKey];
      if (!arr.keys.includes(enumKey) && valueOfObj !== undefined)
        return {
          keys: [...arr.keys, enumKey],
          values: [...arr.values, valueOfObj],
        };
      return arr;
    },
    {
      keys: [] as Array<keyof typeof originEnum>,
      values: [] as Array<ValueOf<typeof originEnum>>,
    },
  );

  return result;
};

const orderTypeEnumsObj: EnumObjBase<typeof OrderTypeEnums> = {
  BUY: {
    label: 'buyOrderTypeEnums',
    value: OrderTypeEnums.BUY,
  },
  SELL: {
    label: 'sellOrderTypeEnums',
    value: OrderTypeEnums.SELL,
  },
};

const orderStatusEnumsObj: EnumObjBase<typeof OrderStatusEnums> = {
  PENDING: {
    label: 'pendingOrderStatusEnums',
    value: OrderStatusEnums.PENDING,
  },
  CONFIRMED: {
    label: 'confirmedOrderStatusEnums',
    value: OrderStatusEnums.CONFIRMED,
  },
  PROCESSING: {
    label: 'processingOrderStatusEnums',
    value: OrderStatusEnums.PROCESSING,
  },
  COMPLETED: {
    label: 'completedOrderStatusEnums',
    value: OrderStatusEnums.COMPLETED,
  },
  CANCELLED: {
    label: 'cancelledOrderStatusEnums',
    value: OrderStatusEnums.CANCELLED,
  },
  REJECTED: {
    label: 'rejectedOrderStatusEnums',
    value: OrderStatusEnums.REJECTED,
  },
};

const currencyCodeEnumsObj: EnumObjBase<typeof CurrencyCodeEnums> = {
  TWD: { label: 'twdCurrencyEnums', value: CurrencyCodeEnums.TWD },
  HKD: { label: 'hkdCurrencyEnums', value: CurrencyCodeEnums.HKD },
  JPY: { label: 'jpyCurrencyEnums', value: CurrencyCodeEnums.JPY },
  CNY: { label: 'cnyCurrencyEnums', value: CurrencyCodeEnums.CNY },
  SGD: { label: 'sgdCurrencyEnums', value: CurrencyCodeEnums.SGD },
  MYR: { label: 'myrCurrencyEnums', value: CurrencyCodeEnums.MYR },
  KRW: { label: 'krwCurrencyEnums', value: CurrencyCodeEnums.KRW },
  THB: { label: 'thbCurrencyEnums', value: CurrencyCodeEnums.THB },
  USD: { label: 'usdCurrencyEnums', value: CurrencyCodeEnums.USD },
  GBP: { label: 'gbpCurrencyEnums', value: CurrencyCodeEnums.GBP },
  VND: { label: 'vndCurrencyEnums', value: CurrencyCodeEnums.VND },
  PHP: { label: 'phpCurrencyEnums', value: CurrencyCodeEnums.PHP },
};

const orderTypeEnumsOptions = Object.keys(OrderTypeEnums).map((key) => {
  const enumKey = key as keyof typeof OrderTypeEnums;
  const { label = 'undefined', value } = orderTypeEnumsObj[enumKey];
  return { label, value };
});

const orderStatusEnumsOptions = Object.keys(OrderStatusEnums).map((key) => {
  const enumKey = key as keyof typeof OrderStatusEnums;
  const { label = 'undefined', value } = orderStatusEnumsObj[enumKey];
  return { label, value };
});

const currencyCodeEnumsOptions = Object.keys(CurrencyCodeEnums).map((key) => {
  const enumKey = key as keyof typeof CurrencyCodeEnums;
  const { label = 'undefined', value } = currencyCodeEnumsObj[enumKey];
  return { label, value };
});

export {
  orderTypeEnumsOptions,
  orderStatusEnumsOptions,
  currencyCodeEnumsOptions
};
