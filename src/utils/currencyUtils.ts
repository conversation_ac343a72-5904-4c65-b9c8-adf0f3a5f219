import { OrderStatusEnums } from '@/utils/enums';

/**
 * Get currency symbol for display
 */
export const getCurrencySymbol = (currencyCode: string): string => {
  const symbols: Record<string, string> = {
    TWD: 'NT$',
    HKD: 'HK$',
    JPY: '¥',
    CNY: '¥',
    SGD: 'S$',
    MYR: 'RM',
    KRW: '₩',
    THB: '฿',
    USD: '$',
    GBP: '£',
    VND: '₫',
    PHP: '₱',
  };
  
  return symbols[currencyCode] || currencyCode;
};

/**
 * Format currency amount with symbol
 */
export const formatOrderAmount = (amount: number, currencyCode: string): string => {
  const symbol = getCurrencySymbol(currencyCode);
  return `${symbol}${amount.toLocaleString()}`;
};

/**
 * Get status color for UI components
 */
export const getOrderStatusColor = (status: OrderStatusEnums | string): string => {
  const statusUpper = typeof status === 'string' ? status.toUpperCase() : status;
  
  switch (statusUpper) {
    case OrderStatusEnums.COMPLETED:
      return 'success';
    case OrderStatusEnums.CONFIRMED:
      return 'processing';
    case OrderStatusEnums.PROCESSING:
      return 'processing';
    case OrderStatusEnums.PENDING:
      return 'warning';
    case OrderStatusEnums.CANCELLED:
      return 'default';
    case OrderStatusEnums.REJECTED:
      return 'error';
    default:
      return 'default';
  }
};
