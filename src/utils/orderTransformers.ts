import type { Order, OrderWithComputed, LegacyOrder, BackendPaginatedResponse, PaginatedResponse } from '@/types';
import { OrderStatusEnums } from '@/utils/enums';

/**
 * Transform backend Order to frontend OrderWithComputed
 * Adds computed properties and compatibility fields
 */
export const transformBackendOrder = (backendOrder: Order): OrderWithComputed => {
  const totalPrice = backendOrder.quantity * backendOrder.pricePerUnit;
  
  return {
    ...backendOrder,
    // Computed properties
    totalPrice,
    
    // Compatibility properties for existing UI components
    id: backendOrder.orderCode,
    status: backendOrder.orderStatus.toLowerCase(),
    type: backendOrder.orderType,
    amount: totalPrice,
    date: backendOrder.createdAt,
  };
};

/**
 * Transform backend paginated response to frontend format
 */
export const transformBackendPaginatedResponse = <T, U>(
  backendResponse: BackendPaginatedResponse<T>,
  transformer: (item: T) => U
): PaginatedResponse<U> => {
  return {
    data: backendResponse.items.map(transformer),
    total: backendResponse.totalItems,
    page: backendResponse.pageNumber,
    pageSize: backendResponse.pageSize,
  };
};

/**
 * Transform backend Order response to frontend format
 */
export const transformOrdersResponse = (
  backendResponse: BackendPaginatedResponse<Order>
): PaginatedResponse<OrderWithComputed> => {
  return transformBackendPaginatedResponse(backendResponse, transformBackendOrder);
};

/**
 * Get status color for UI components
 */
export const getOrderStatusColor = (status: OrderStatusEnums | string): string => {
  const statusUpper = typeof status === 'string' ? status.toUpperCase() : status;
  
  switch (statusUpper) {
    case OrderStatusEnums.COMPLETED:
      return 'success';
    case OrderStatusEnums.CONFIRMED:
      return 'processing';
    case OrderStatusEnums.PROCESSING:
      return 'processing';
    case OrderStatusEnums.PENDING:
      return 'warning';
    case OrderStatusEnums.CANCELLED:
      return 'default';
    case OrderStatusEnums.REJECTED:
      return 'error';
    default:
      return 'default';
  }
};

/**
 * Get currency symbol for display
 */
export const getCurrencySymbol = (currencyCode: string): string => {
  const symbols: Record<string, string> = {
    TWD: 'NT$',
    HKD: 'HK$',
    JPY: '¥',
    CNY: '¥',
    SGD: 'S$',
    MYR: 'RM',
    KRW: '₩',
    THB: '฿',
    USD: '$',
    GBP: '£',
    VND: '₫',
    PHP: '₱',
  };
  
  return symbols[currencyCode] || currencyCode;
};

/**
 * Format currency amount with symbol
 */
export const formatOrderAmount = (amount: number, currencyCode: string): string => {
  const symbol = getCurrencySymbol(currencyCode);
  return `${symbol}${amount.toLocaleString()}`;
};

/**
 * Transform legacy Order to new Order format (for migration)
 */
export const transformLegacyOrder = (legacyOrder: LegacyOrder): Order => {
  return {
    orderId: parseInt(legacyOrder.id) || 0,
    orderCode: legacyOrder.id,
    orderType: legacyOrder.type,
    userId: legacyOrder.memberId,
    counterId: '0001', // Default counter ID
    staffId: legacyOrder.createdBy || '',
    quantity: 1, // Default quantity
    pricePerUnit: legacyOrder.amount,
    currencyUnit: 'USD' as any, // Default currency
    orderStatus: legacyOrder.status.toUpperCase() as any,
    notes: legacyOrder.notes || '',
    metadata: null,
    createdAt: legacyOrder.date,
    updatedAt: legacyOrder.updatedAt || legacyOrder.date,
  };
};

/**
 * Check if we should use mock data based on environment
 */
export const shouldUseMockData = (): boolean => {
  return import.meta.env.VITE_MOCK_API === 'true';
};

/**
 * Map backend field names to frontend filter field names
 */
export const mapFilterFieldToBackend = (frontendField: string): string => {
  const fieldMapping: Record<string, string> = {
    // Frontend -> Backend field mapping
    'id': 'orderCode',
    'status': 'orderStatus',
    'type': 'orderType',
    'amount': 'pricePerUnit', // or totalPrice calculation
    'date': 'createdAt',
    'memberId': 'userId',
    'memberName': 'userId', // Will need user lookup
    'createdBy': 'staffId',
    // Direct mappings
    'orderId': 'orderId',
    'orderCode': 'orderCode',
    'orderType': 'orderType',
    'userId': 'userId',
    'counterId': 'counterId',
    'staffId': 'staffId',
    'quantity': 'quantity',
    'pricePerUnit': 'pricePerUnit',
    'totalPrice': 'totalPrice', // Computed field
    'currencyUnit': 'currencyUnit',
    'orderStatus': 'orderStatus',
    'createdAt': 'createdAt',
    'updatedAt': 'updatedAt',
  };
  
  return fieldMapping[frontendField] || frontendField;
};

/**
 * Map backend field names to frontend display field names
 */
export const mapBackendFieldToFrontend = (backendField: string): string => {
  const fieldMapping: Record<string, string> = {
    // Backend -> Frontend field mapping
    'orderCode': 'id',
    'orderStatus': 'status',
    'orderType': 'type',
    'userId': 'memberId',
    'staffId': 'createdBy',
    'createdAt': 'date',
    // Direct mappings that don't need transformation
    'orderId': 'orderId',
    'counterId': 'counterId',
    'quantity': 'quantity',
    'pricePerUnit': 'pricePerUnit',
    'currencyUnit': 'currencyUnit',
    'updatedAt': 'updatedAt',
    'notes': 'notes',
    'metadata': 'metadata',
  };
  
  return fieldMapping[backendField] || backendField;
};
