import { Tag } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { CurrencyCodeEnums } from '@/utils/enums';
import { currencyCodeEnumsOptions } from '@/utils/options';
import { getCurrencySymbol } from '@/utils/orderTransformers';

interface CurrencyTagProps {
  currency: CurrencyCodeEnums | string;
  showSymbol?: boolean;
  className?: string;
}

export default function CurrencyTag({ 
  currency, 
  showSymbol = false,
  className = '' 
}: CurrencyTagProps) {
  const { t } = useTranslation('options');

  const currencyEnum = useMemo(() => {
    if (typeof currency === 'string') {
      return currency.toUpperCase() as CurrencyCodeEnums;
    }
    return currency;
  }, [currency]);

  const symbol = useMemo(() => {
    return getCurrencySymbol(currencyEnum);
  }, [currencyEnum]);

  const label = useMemo(() => {
    const option = currencyCodeEnumsOptions.find(
      (option) => option.value === currencyEnum
    );
    const translatedLabel = t(option?.label || 'undefined');
    
    if (showSymbol) {
      return `${symbol} ${translatedLabel}`;
    }
    return translatedLabel;
  }, [currencyEnum, t, symbol, showSymbol]);

  const color = useMemo(() => {
    // Different colors for different currency types
    switch (currencyEnum) {
      case CurrencyCodeEnums.USD:
        return 'green';
      case CurrencyCodeEnums.EUR:
        return 'blue';
      case CurrencyCodeEnums.GBP:
        return 'purple';
      case CurrencyCodeEnums.JPY:
        return 'orange';
      case CurrencyCodeEnums.CNY:
        return 'red';
      case CurrencyCodeEnums.TWD:
        return 'cyan';
      case CurrencyCodeEnums.HKD:
        return 'geekblue';
      default:
        return 'default';
    }
  }, [currencyEnum]);

  return (
    <Tag color={color} className={`!m-0 w-fit ${className}`}>
      {label}
    </Tag>
  );
}
