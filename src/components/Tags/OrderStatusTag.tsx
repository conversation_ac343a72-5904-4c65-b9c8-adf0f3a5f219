import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  ExclamationCircleOutlined,
  SyncOutlined,
  ClockCircleOutlined,
  StopOutlined,
} from '@ant-design/icons';
import { Tag } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { OrderStatusEnums } from '@/utils/enums';
import { orderStatusEnumsOptions } from '@/utils/options';
import { getOrderStatusColor } from '@/utils/currencyUtils';

interface OrderStatusTagProps {
  status: OrderStatusEnums | string;
  className?: string;
}

export default function OrderStatusTag({
  status,
  className = ''
}: OrderStatusTagProps) {
  const { t } = useTranslation('options');

  const statusEnum = useMemo(() => {
    if (typeof status === 'string') {
      return status.toUpperCase() as OrderStatusEnums;
    }
    return status;
  }, [status]);

  const color = useMemo(() => {
    return getOrderStatusColor(statusEnum);
  }, [statusEnum]);

  const icon = useMemo(() => {
    switch (statusEnum) {
      case OrderStatusEnums.PENDING:
        return <ClockCircleOutlined />;
      case OrderStatusEnums.CONFIRMED:
        return <SyncOutlined />;
      case OrderStatusEnums.PROCESSING:
        return <SyncOutlined spin />;
      case OrderStatusEnums.COMPLETED:
        return <CheckCircleOutlined />;
      case OrderStatusEnums.CANCELLED:
        return <StopOutlined />;
      case OrderStatusEnums.REJECTED:
        return <CloseCircleOutlined />;
      default:
        return <ExclamationCircleOutlined />;
    }
  }, [statusEnum]);

  const label = useMemo(() => {
    const option = orderStatusEnumsOptions.find(
      (option) => option.value === statusEnum
    );
    return t(option?.label || 'undefined');
  }, [statusEnum, t]);

  return (
    <Tag color={color} icon={icon} className={`!m-0 capitalize w-fit ${className}`}>
      {label}
    </Tag>
  );
}
