import { Tag } from 'antd';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { OrderTypeEnums } from '@/utils/enums';
import { orderTypeEnumsOptions } from '@/utils/options';

interface OrderTypeTagProps {
  type: OrderTypeEnums | string;
  className?: string;
}

export default function OrderTypeTag({ type, className = '' }: OrderTypeTagProps) {
  const { t } = useTranslation('options');

  const typeEnum = useMemo(() => {
    if (typeof type === 'string') {
      return type.toUpperCase() as OrderTypeEnums;
    }
    return type;
  }, [type]);

  const color = useMemo(() => {
    switch (typeEnum) {
      case OrderTypeEnums.BUY:
        return 'success';
      case OrderTypeEnums.SELL:
        return 'error';
      default:
        return 'default';
    }
  }, [typeEnum]);

  const label = useMemo(() => {
    const option = orderTypeEnumsOptions.find((option) => option.value === typeEnum);
    return t(option?.label || 'undefined');
  }, [typeEnum, t]);

  return (
    <Tag color={color} className={`!m-0 capitalize w-fit ${className}`}>
      {label}
    </Tag>
  );
}
