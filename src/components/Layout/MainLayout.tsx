import { Layout } from 'antd';
import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { Outlet } from 'react-router-dom';
import Header from './Header';
import Sidebar from './Sidebar';
import { useNavigation } from '@/hooks/useNavigation.ts';
import { useResponsive } from '@/hooks/useResponsive.ts';
import { useUserStore } from '@/stores';

const { Content } = Layout;

const MainLayout: React.FC = () => {
  const [collapsed, setCollapsed] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const { isDark } = useUserStore();
  const { t } = useTranslation('mainLayout');
  const { isMobile, isTablet } = useResponsive();
  const { getCurrentPage } = useNavigation();

  // Auto-collapse sidebar on mobile and tablet
  useEffect(() => {
    if (isMobile) {
      setCollapsed(true);
    } else if (isTablet) {
      setCollapsed(true);
    } else {
      setCollapsed(false);
    }
  }, [isMobile, isTablet]);

  const toggleSidebar = () => {
    if (isMobile) {
      setMobileMenuOpen(!mobileMenuOpen);
    } else {
      setCollapsed(!collapsed);
    }
  };

  const closeMobileMenu = () => {
    if (isMobile) {
      setMobileMenuOpen(false);
    }
  };

  // Get page title based on current route
  const getPageTitle = () => {
    const pageKey = getCurrentPage();
    switch (pageKey) {
      case 'dashboard':
        return t('dashboard');
      case 'members':
        return t('members');
      case 'orders':
        return t('orders');
      case 'management':
        return t('management');
      case 'accounting':
        return t('accounting');
      case 'rolePermission':
        return t('rolePermission');
      case 'settings':
        return t('settings');
      default:
        return t('dashboard');
    }
  };

  return (
    <Layout>
      {/* Mobile Overlay */}
      {isMobile && mobileMenuOpen && (
        <div
          className='fixed inset-0 bg-black/40 bg-opacity-50 z-40'
          onClick={closeMobileMenu}
        />
      )}

      <Sidebar
        collapsed={collapsed}
        selectedKey={getCurrentPage()}
        isMobile={isMobile}
        mobileMenuOpen={mobileMenuOpen}
        onMobileMenuClose={closeMobileMenu}
      />

      <Layout
        className={isMobile ? 'ml-0' : collapsed ? 'ml-20' : 'ml-70'}
        style={{ marginLeft: isMobile ? 0 : collapsed ? 80 : 280 }}
      >
        <Header
          collapsed={collapsed}
          onToggle={toggleSidebar}
          title={getPageTitle()}
          isMobile={isMobile}
          isTablet={isTablet}
        />
        <Content
          className={`${isMobile ? 'p-3' : isTablet ? 'p-4' : 'p-6'} ${isDark ? 'bg-[#29446f]' : 'bg-gray-200'}`}
        >
          <div
            className={`${
              isDark
                ? 'bg-[#1e293b] text-gray-100 shadow-md shadow-black/30'
                : 'bg-white text-gray-800 shadow-sm'
            } rounded-lg shadow-sm ${isMobile ? 'p-3' : isTablet ? 'p-4' : 'p-6'} min-h-full`}
          >
            <Outlet />
          </div>
        </Content>
      </Layout>
    </Layout>
  );
};

export default MainLayout;
