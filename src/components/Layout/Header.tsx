import {
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  UserOutlined,
  SettingOutlined,
  LogoutOutlined,
  SearchOutlined,
  DownOutlined,
  SunOutlined,
  MoonOutlined,
} from '@ant-design/icons';
import { Layout, Button, Typography, Avatar, Dropdown, Switch } from 'antd';
import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import DropdownLocale from '../DropdownYF/DropdownLocale';
import NotificationDropdown from './Notification/NotificationDropdown';
import { useLogout } from '@/hooks';
import { useNavigation } from '@/hooks/useNavigation.ts';
import { useUserStore } from '@/stores';

const { Header: AntHeader } = Layout;
const { Title, Text } = Typography;

interface HeaderProps {
  collapsed: boolean;
  onToggle: () => void;
  title: string;
  isMobile?: boolean;
  isTablet?: boolean;
}

const Header: React.FC<HeaderProps> = ({
  collapsed,
  onToggle,
  title,
  isMobile = false,
  isTablet = false,
}) => {
  const { t } = useTranslation('header');
  const logout = useLogout();
  const { navigateTo } = useNavigation();
  const { setIsDark, isDark } = useUserStore();
  const userMenuItems = useMemo(
    () => [
      {
        key: 'profile',
        icon: (
          <UserOutlined
            className={isDark ? 'text-gray-300' : 'text-gray-600'}
          />
        ),
        label: (
          <span
            className={
              isDark ? 'text-gray-200 font-medium' : 'text-gray-700 font-medium'
            }
          >
            {t('viewProfile')}
          </span>
        ),
        onClick: () => navigateTo.profile(),
      },
      {
        key: 'settings',
        icon: (
          <SettingOutlined
            className={isDark ? 'text-gray-300' : 'text-gray-600'}
          />
        ),
        label: (
          <span
            className={
              isDark ? 'text-gray-200 font-medium' : 'text-gray-700 font-medium'
            }
          >
            {t('settings')}
          </span>
        ),
        onClick: () => navigateTo.settings(),
      },
      {
        type: 'divider' as const,
      },
      {
        key: 'logout',
        icon: <LogoutOutlined className='text-red-500' />,
        label: <span className='text-red-500 font-medium'>{t('signOut')}</span>,
        onClick: logout,
      },
    ],
    [logout, isDark, navigateTo, t],
  );

  return (
    <AntHeader
      style={{
        background: isDark ? '#1f2937' : 'rgba(255, 255, 255)',
        backdropFilter: 'blur(12px)',
        borderBottom: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
        boxShadow: '0 2px 8px rgba(0, 0, 0, 0.06)',
        height: isMobile ? '64px' : isTablet ? '80px' : '96px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        position: 'sticky',
        zIndex: 100,
        top: '0px',
        padding: isMobile ? '0 12px' : isTablet ? '0 16px' : '0 24px',
      }}
    >
      {/* Left Section */}
      <div
        className={`flex items-center ${isMobile ? 'gap-3' : isTablet ? 'gap-4' : 'gap-6'}`}
      >
        <Button
          type='text'
          icon={
            isMobile ? (
              <MenuUnfoldOutlined />
            ) : collapsed ? (
              <MenuUnfoldOutlined />
            ) : (
              <MenuFoldOutlined />
            )
          }
          onClick={onToggle}
          style={{
            width: isMobile ? '36px' : isTablet ? '40px' : '44px',
            height: isMobile ? '36px' : isTablet ? '40px' : '44px',
            borderRadius: isMobile ? '8px' : '12px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            fontSize: isMobile ? '16px' : '18px',
            color: isDark ? '#d1d5db' : '#4b5563',
            transition: 'all 0.2s ease',
          }}
          className='hover:bg-gray-50 dark:hover:bg-gray-800 hover:text-gray-900 dark:hover:text-white'
        />

        <div className={`flex items-center ${isMobile ? 'gap-2' : 'gap-4'}`}>
          <div
            style={{
              width: '4px',
              height: isMobile ? '24px' : '32px',
              background: 'linear-gradient(180deg, #1890ff 0%, #096dd9 100%)',
              borderRadius: '2px',
            }}
          ></div>
          <div className='flex flex-col justify-center'>
            <Title
              level={4}
              style={{
                margin: 0,
                color: isDark ? '#f9fafb' : '#1f2937',
                fontWeight: 600,
                fontSize: isMobile ? '16px' : isTablet ? '18px' : '20px',
              }}
            >
              <span>{title}</span>
            </Title>
            {!isMobile && (
              <Text>
                <span
                  className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium`}
                >
                  {t('subtitle')}
                </span>
              </Text>
            )}
          </div>
        </div>
      </div>

      {/* Right Section */}
      <div className={`flex items-center ${isMobile ? 'gap-x-1' : 'gap-x-2'}`}>
        {/* Search Button - Hide on mobile */}
        {!isMobile && (
          <Button
            type='text'
            icon={<SearchOutlined />}
            size={isTablet ? 'middle' : 'large'}
            className='hover:bg-gray-50'
          />
        )}

        {/* Notifications */}
        <NotificationDropdown isMobile={isMobile} />
        <DropdownLocale />

        {/* Theme Switch */}
        <Switch
          className='relative bottom-[2px]'
          checked={!isDark}
          onChange={() => setIsDark(!isDark)}
          checkedChildren={<SunOutlined />}
          unCheckedChildren={<MoonOutlined />}
        />
        {/* Divider - Hide on mobile */}
        {!isMobile && (
          <div
            style={{
              width: '1px',
              height: '24px',
              backgroundColor: isDark ? '#4b5563' : '#e5e7eb',
              margin: '0 8px',
            }}
          ></div>
        )}

        {/* User Menu */}
        <Dropdown
          menu={{
            items: userMenuItems,
            style: {
              borderRadius: '12px',
              boxShadow: '0 10px 25px rgba(0, 0, 0, 0.1)',
              border: `1px solid ${isDark ? '#374151' : '#f0f0f0'}`,
              backgroundColor: isDark ? '#1f2937' : '#fff',
              color: isDark ? '#f9fafb' : '#1f2937',
              padding: '8px',
            },
          }}
          placement={isMobile ? 'bottomRight' : 'bottomCenter'}
          trigger={['click']}
          arrow={{ pointAtCenter: true }}
        >
          <div
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: isMobile ? '8px' : '12px',
              cursor: 'pointer',
              padding: isMobile ? '6px 8px' : '8px 12px',
              borderRadius: isMobile ? '8px' : '12px',
              transition: 'all 0.2s ease',
              border: '1px solid transparent',
            }}
          >
            <Avatar
              size={isMobile ? 28 : isTablet ? 32 : 36}
              icon={<UserOutlined />}
              style={{
                background: 'linear-gradient(135deg, #1890ff 0%, #722ed1 100%)',
                boxShadow: '0 2px 8px rgba(24, 144, 255, 0.3)',
              }}
            />
            {!isMobile && (
              <div className='hidden md:flex gap-1 flex-col'>
                <Text>
                  <span
                    className={`${isTablet ? 'text-xs' : 'text-sm'} font-semibold ${isDark ? 'text-gray-100' : 'text-gray-900'} block leading-tight`}
                  >
                    Admin User
                  </span>
                </Text>
                <Text>
                  <span
                    className={`text-xs ${isDark ? 'text-gray-400' : 'text-gray-500'} leading-tight`}
                  >
                    Administrator
                  </span>
                </Text>
              </div>
            )}
            {!isMobile && (
              <DownOutlined
                style={{
                  fontSize: '10px',
                  color: isDark ? '#9ca3af' : '#9ca3af',
                  transition: 'transform 0.2s ease',
                }}
                className='hidden md:block'
              />
            )}
          </div>
        </Dropdown>
      </div>
    </AntHeader>
  );
};

export default Header;
