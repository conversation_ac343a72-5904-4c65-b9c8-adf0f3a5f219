import {
  DashboardOutlined,
  UserOutlined,
  ShoppingCartOutlined,
  SettingOutlined,
  CalculatorOutlined,
  StarFilled,
  SafetyOutlined,
  TeamOutlined,
} from '@ant-design/icons';
import { Layout, Menu, Typography } from 'antd';
import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { ROUTES } from '@/constants/routes.ts';
import { useUserStore } from '@/stores';
import type { MenuItem } from '@/types';

const { Sider } = Layout;
const { Title, Text } = Typography;

interface SidebarProps {
  collapsed: boolean;
  selectedKey: string;
  isMobile?: boolean;
  mobileMenuOpen?: boolean;
  onMobileMenuClose?: () => void;
}

const Sidebar: React.FC<SidebarProps> = ({
  collapsed,
  selectedKey,
  isMobile = false,
  mobileMenuOpen = false,
  onMobileMenuClose,
}) => {
  const { t } = useTranslation('sidebar');
  const navigate = useNavigate();
  const { isDark } = useUserStore();

  const handleMenuSelect = (key: string) => {
    const routes: Record<string, string> = {
      dashboard: ROUTES.DASHBOARD,
      members: ROUTES.MEMBERS,
      orders: ROUTES.ORDERS,
      management: ROUTES.MANAGEMENT,
      accounting: ROUTES.ACCOUNTING,
      rolePermission: ROUTES.ROLE_PERMISSION,
      settings: ROUTES.SETTINGS,
    };

    if (routes[key]) {
      navigate(routes[key]);
      // Close mobile menu after navigation
      if (isMobile && onMobileMenuClose) {
        onMobileMenuClose();
      }
    }
  };

  const menuItems: MenuItem[] = [
    {
      key: 'dashboard',
      label: t('dashboard'),
      icon: <DashboardOutlined />,
      path: ROUTES.DASHBOARD,
    },
    {
      key: 'members',
      label: t('members'),
      icon: <UserOutlined />,
      path: ROUTES.MEMBERS,
    },
    {
      key: 'orders',
      label: t('orders'),
      icon: <ShoppingCartOutlined />,
      path: ROUTES.ORDERS,
    },
    {
      key: 'management',
      label: t('management'),
      icon: <TeamOutlined />,
      path: ROUTES.MANAGEMENT,
    },
    {
      key: 'accounting',
      label: t('accounting'),
      icon: <CalculatorOutlined />,
      path: ROUTES.ACCOUNTING,
    },
    {
      key: 'rolePermission',
      label: t('rolePermission'),
      icon: <SafetyOutlined />,
      path: ROUTES.ROLE_PERMISSION,
    },
    {
      key: 'settings',
      label: t('settings'),
      icon: <SettingOutlined />,
      path: ROUTES.SETTINGS,
    },
  ];

  // const handleLanguageChange = (language: Language) => {
  //   i18n.changeLanguage(language);
  //   localStorage.setItem('language', language);
  // };

  // Determine sidebar visibility and styling based on device type
  const sidebarStyle = isMobile
    ? {
        position: 'fixed' as const,
        top: '0px',
        left: mobileMenuOpen ? '0px' : '-280px',
        transition: 'left 0.3s ease',
        zIndex: 50,
      }
    : {
        position: 'fixed' as const,
        top: '0px',
        left: '0px',
      };

  const sidebarWidth = isMobile ? 280 : collapsed ? 80 : 280;

  return (
    <Sider
      trigger={null}
      collapsible
      collapsed={isMobile ? false : collapsed}
      style={sidebarStyle}
      className={`${isDark ? ' sidebar-dark' : 'sidebar-light'} h-screen max-h-screen shadow-2xl ${isMobile ? 'z-50' : 'z-40'}`}
      width={sidebarWidth}
    >
      {/* Logo Section */}
      <div
        className={`${collapsed ? 'px-4 justify-center' : 'px-6'} ${isMobile ? 'opacity-0 h-16' : 'h-24'} flex items-center py-5 border-b border-white/10`}
      >
        {!collapsed || isMobile ? (
          <div className='flex items-center gap-3'>
            <div
              className={`${isMobile ? 'w-8 h-8' : 'w-10 h-10'} bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg`}
            >
              <StarFilled
                className={`text-white ${isMobile ? 'text-base' : 'text-lg'}`}
              />
            </div>
            <div>
              <Title
                level={4}
                className={`m-0 font-bold ${isMobile ? 'text-base' : ''}`}
              >
                <span>{t('title')}</span>
              </Title>
              <Text>
                <span
                  className={`${isMobile ? 'text-xs' : 'text-xs'} font-medium ${isDark ? 'text-slate-300' : 'text-gray-400'}`}
                >
                  {t('subtitle')}
                </span>
              </Text>
            </div>
          </div>
        ) : (
          <div className='flex justify-center'>
            <div className='w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg'>
              <StarFilled className='text-white text-lg' />
            </div>
          </div>
        )}
      </div>

      {/* Navigation Menu */}
      <div className={`${isMobile ? 'px-2 py-3' : 'px-3 py-4'}`}>
        {(!collapsed || isMobile) && (
          <Text>
            <span
              className={`text-xs font-semibold ${isDark ? 'text-gray-500' : 'text-gray-600'} uppercase tracking-wider ${isMobile ? 'px-2' : 'px-3'} mb-3 block`}
            >
              {t('navigation')}
            </span>
          </Text>
        )}
        <Menu
          mode='inline'
          selectedKeys={[selectedKey]}
          className={`${isDark ? '!pr-2' : ''} !border-r-0`}
          items={menuItems.map((item) => ({
            key: item.key,
            icon: (
              <div className={`${isMobile ? 'text-base' : 'text-lg'}`}>
                {item.icon}
              </div>
            ),
            label: (
              <span className={`font-medium ${isMobile ? 'text-sm' : ''}`}>
                {item.label}
              </span>
            ),
            onClick: () => handleMenuSelect(item.key),
            className: `${isMobile ? 'mb-1' : 'mb-1'} rounded-lg mx-0 ${selectedKey === item.key ? 'bg-primary/10 border-r-2 border-primary' : 'hover:bg-gray-50'}`,
          }))}
        />
      </div>

      {/* Language Selector */}
      {/* {(!collapsed || isMobile) && ( */}
      {/*   <div */}
      {/*     className={`absolute ${isMobile ? 'bottom-4 left-3 right-3' : 'bottom-6 left-4 right-4'}`} */}
      {/*   > */}
      {/*     <Divider className='my-4' /> */}
      {/*     <div */}
      {/*       className={`${isDark ? 'bg-[#4e5f77]' : 'bg-[#698dcf]'} rounded-lg ${isMobile ? 'p-3' : 'p-4'}`} */}
      {/*     > */}
      {/*       <div className='flex items-center gap-2 mb-3'> */}
      {/*         <GlobalOutlined /> */}
      {/*         <Text className='text-sm font-medium text-white'> */}
      {/*           <span className='text-xs font-semibold text-black uppercase tracking-wider'> */}
      {/*             {t('language')} */}
      {/*           </span> */}
      {/*         </Text> */}
      {/*       </div> */}
      {/*       <Select */}
      {/*         value={i18n.language as Language} */}
      {/*         onChange={handleLanguageChange} */}
      {/*         className='w-full' */}
      {/*         size={isMobile ? 'small' : 'middle'} */}
      {/*         variant='borderless' */}
      {/*         popupClassName={`${isDark ? 'select-dropdown-dark' : 'select-dropdown-light'}`} */}
      {/*       > */}
      {/*         <Option value='en'> */}
      {/*           <div className='flex items-center gap-2'> */}
      {/*             <span className={`${isMobile ? 'text-sm' : 'text-base'}`}> */}
      {/*               🇺🇸 */}
      {/*             </span> */}
      {/*             <span className={`${isMobile ? 'text-xs' : ''}`}> */}
      {/*               {t('english')} */}
      {/*             </span> */}
      {/*           </div> */}
      {/*         </Option> */}
      {/*         <Option value='tw'> */}
      {/*           <div className='flex items-center gap-2'> */}
      {/*             <span className={`${isMobile ? 'text-sm' : 'text-base'}`}> */}
      {/*               🇹🇼 */}
      {/*             </span> */}
      {/*             <span className={`${isMobile ? 'text-xs' : ''}`}> */}
      {/*               {t('taiwan')} */}
      {/*             </span> */}
      {/*           </div> */}
      {/*         </Option> */}
      {/*       </Select> */}
      {/*     </div> */}
      {/*   </div> */}
      {/* )} */}
    </Sider>
  );
};

export default Sidebar;
