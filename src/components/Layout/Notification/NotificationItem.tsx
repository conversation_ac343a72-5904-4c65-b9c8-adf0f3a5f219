import { Button, Flex, Typography } from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import type { NotificationItem } from './NotificationDropdown';
import { OrderStatusTag } from '@/components/Tags';
import { useUserStore } from '@/stores';
import { dateFormator } from '@/utils/dateUtils';

const { Text } = Typography;

interface NotificationItemProps {
  notification: NotificationItem;
  onClick: (orderId: NotificationItem['id']) => void;
  isMobile?: boolean;
}
export default function NotificationItem(props: NotificationItemProps) {
  const { notification, onClick, isMobile = false } = props || {};
  const { t } = useTranslation('notificationItem');
  const { isDark } = useUserStore();
  const color = useMemo(() => {
    switch (notification.status) {
      case 'pending':
        return 'border-blue-400';
      case 'completed':
        return 'border-green-400';
      case 'cancelled':
        return 'border-yellow-400';
      case 'failed':
        return 'border-red-400';
    }
  }, [notification.status]);

  return (
    <Flex
      vertical
      gap={isMobile ? 6 : 8}
      className={`${color} !-mx-2 rounded-md border-l-4 !px-2 !py-2 transition-colors ${isDark ? 'hover:bg-gray-800' : 'hover:bg-gray-100'}`}
    >
      <Flex
        align='center'
        justify='space-between'
        gap={isMobile ? 6 : 8}
        className='w-full'
      >
        <Flex vertical className={`${isMobile ? 'ml-2' : 'ml-4'}`}>
          <Text strong className={`${isMobile ? 'text-sm' : ''}`}>
            {notification.memberName}
          </Text>
          <Text type='secondary' className={`${isMobile ? 'text-xs' : ''}`}>
            {notification.description}
          </Text>
          <Text type='secondary' className={`${isMobile ? 'text-xs' : ''}`}>
            {dayjs(notification.date).format(dateFormator.accurate)}
          </Text>
        </Flex>
        <OrderStatusTag status={notification.status} />
      </Flex>
      <Button
        key='mark'
        size={isMobile ? 'small' : 'small'}
        className='w-fit'
        onClick={() => onClick(notification.id)}
      >
        <span className={`${isMobile ? 'text-xs' : ''}`}>
          {t('markAsRead')}
        </span>
      </Button>
    </Flex>
  );
}
