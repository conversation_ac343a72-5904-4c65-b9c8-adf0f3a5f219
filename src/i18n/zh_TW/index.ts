import {
  dropdownLocale,
  notificationDropdown,
  notificationItem,
  header,
  mainLayout,
  sidebar,
  orderStatusTag,
  dayPicker,
  errorBoundary,
  galaxyTable,
  loadingPage,
} from './components';
import {
  dashboard,
  login,
  memberForm,
  memberDetailsModal,
  members,
  notFound,
  orderDetailsModal,
  orders,
  profileSettings,
  securitySettings,
  profile,
  backupSettings,
  generalSettings,
  notificationSettings,
  systemSettings,
  settings,
  accounting,
  financialStatements,
  reconciliationReport,
  transactionSummaries,
  generateReportModal,
  generateStatementModal,
  generateSummaryModal,
  management,
  employeeDetailsModal,
  frontDeskManagement,
  storesManagement,
  editRateModal,
  rateManagement,
  rateSettingsModal,
  useRateManagementColumns,
  attendanceModal,
  editEmployeeModal,
  addStaffModal,
  staffManagement,
  useStaffManagementColumns,
  employeesManagement,
  rolePermissionManagement,
  permissionsList,
  rolesList,
  userRolesList,
  assignRoleModal,
  createRoleModal,
  editRoleModal,
} from './pages';
import { options } from './utils';

export default {
  dropdownLocale,
  notificationDropdown,
  notificationItem,
  errorBoundary,
  header,
  mainLayout,
  sidebar,
  orderStatusTag,
  dayPicker,
  galaxyTable,
  loadingPage,
  dashboard,
  login,
  memberForm,
  memberDetailsModal,
  members,
  notFound,
  orderDetailsModal,
  orders,
  profileSettings,
  securitySettings,
  options,
  profile,
  backupSettings,
  generalSettings,
  notificationSettings,
  systemSettings,
  settings,

  accounting,
  financialStatements,
  reconciliationReport,
  transactionSummaries,
  generateReportModal,
  generateStatementModal,
  generateSummaryModal,
  management,
  employeeDetailsModal,
  frontDeskManagement,
  storesManagement,
  editRateModal,
  rateManagement,
  rateSettingsModal,
  useRateManagementColumns,
  attendanceModal,
  editEmployeeModal,

  addStaffModal,
  staffManagement,
  useStaffManagementColumns,
  employeesManagement,
  rolePermissionManagement,
  permissionsList,
  rolesList,
  userRolesList,
  assignRoleModal,
  createRoleModal,
  editRoleModal,
};
