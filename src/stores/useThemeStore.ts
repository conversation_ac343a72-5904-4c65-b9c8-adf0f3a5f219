import { create } from 'zustand';

type ThemeStoreInit = {
  isDark: boolean;
};

interface ThemeStoreOption extends ThemeStoreInit {
  setIsDark: (isDark?: boolean) => void;
}

const init: ThemeStoreInit = {
  isDark: true,
};

const useThemeStore = create<ThemeStoreOption>((set) => {
  return {
    ...init,
    setIsDark: (isDark) => {
      if (isDark === undefined) {
        set((origin) => {
          return { isDark: !origin.isDark };
        });
      } else {
        set({ isDark });
      }
    },
  };
});

export { useThemeStore };
