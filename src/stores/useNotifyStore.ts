import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import type { NotifyBaseOptions } from '@/components/NotifyProvider/NotifyProvider';
import { logWarn } from '@/utils/logger';

type HintItemOptions = {
  id: string;
  title: string;
  des: string;
  createdAt: string;
  isReaded: boolean;
};

type NotifyStoreInit = {
  basicSuccessQue: Array<NotifyBaseOptions>;
  basicErrorQue: Array<NotifyBaseOptions>;
  hintQue: Array<HintItemOptions>;
};

type NotifyStoreOptions = NotifyStoreInit & {
  setBSQ: (basicSuccessQue: Array<NotifyBaseOptions>) => void;
  pushBSQ: (basicSuccessQue: Array<NotifyBaseOptions>) => void;
  shiftBSQ: (count?: number) => void;
  setBEQ: (basicErrorQue: Array<NotifyBaseOptions>) => void;
  pushBEQ: (basicSuccessQue: Array<NotifyBaseOptions>) => void;
  shiftBEQ: (count?: number) => void;
  setHQ: (hintQue: NotifyStoreInit['hintQue']) => void;
  pushHQ: (hintQue: NotifyStoreInit['hintQue']) => void;
  shiftHQ: (count?: number) => void;
  readHQ: (id: string) => void;
};

const init: NotifyStoreInit = {
  basicSuccessQue: [],
  basicErrorQue: [],
  hintQue: [],
};

const useNotifyStore = create(
  persist<NotifyStoreOptions>(
    (set) => ({
      ...init,
      setBSQ: (basicSuccessQue) => {
        set({ basicSuccessQue });
      },
      pushBSQ: (newQue) => {
        set((states) => {
          const basicSuccessQue = [...states.basicSuccessQue, ...newQue];
          return { ...states, basicSuccessQue };
        });
      },
      shiftBSQ: (count) => {
        const useCount = count || 1;
        set((states) => {
          const JsonQue = states.basicSuccessQue.map((screenData) =>
            JSON.stringify(screenData),
          );
          const setQue = Array.from(new Set(JsonQue));
          const getBSQ = (): Array<NotifyBaseOptions> => {
            try {
              const parseQue = setQue.map(
                (screenData) => JSON.parse(screenData) as NotifyBaseOptions,
              );
              return parseQue;
            } catch (error) {
              logWarn({ Title: 'Parse shift BSQ error', error });
              return [];
            }
          };
          const newBSQ = getBSQ();
          const basicSuccessQue = newBSQ.slice(useCount);
          return { ...states, basicSuccessQue };
        });
      },
      setBEQ: (basicErrorQue) => {
        set({ basicErrorQue });
      },
      pushBEQ: (newQue) => {
        set((states) => {
          const basicErrorQue = [...states.basicErrorQue, ...newQue];
          return { ...states, basicErrorQue };
        });
      },
      shiftBEQ: (count) => {
        set((states) => {
          const useCount = count || 1;
          const JsonQue = states.basicErrorQue.map((screenData) =>
            JSON.stringify(screenData),
          );
          const setQue = Array.from(new Set(JsonQue));
          const getBEC = (): Array<NotifyBaseOptions> => {
            try {
              const parseQue = setQue.map(
                (screenData) => JSON.parse(screenData) as NotifyBaseOptions,
              );
              return parseQue;
            } catch (error) {
              logWarn({ Title: 'Parse shift BEQ error', error });
              return [];
            }
          };
          const newQue = getBEC();
          const basicErrorQue = newQue.slice(useCount);
          return { ...states, basicErrorQue };
        });
      },
      setHQ: (hintQue) => {
        set({ hintQue });
      },
      pushHQ: (newQue) => {
        set((states) => {
          const hintQue = [...newQue, ...states.hintQue];
          const uniqueHintQue = Array.from(
            new Map(hintQue.map((item) => [item.id, item])).values(),
          );
          return { ...states, hintQue: uniqueHintQue };
        });
      },
      shiftHQ: (count = 1) => {
        set((states) => {
          const hintQue = states.hintQue.slice(count);
          return { ...states, hintQue };
        });
      },
      readHQ: (id) => {
        set((states) => {
          const pureStates = { ...states };
          const item = pureStates.hintQue.find((findH) => findH.id === id);
          if (!item) return pureStates;
          item.isReaded = true;
          return { ...pureStates, hintQue: [...pureStates.hintQue] };
        });
      },
    }),
    {
      name: 'notify-store',
      partialize: (state) => ({
        basicSuccessQue: state.basicSuccessQue,
        basicErrorQue: state.basicErrorQue,
        hintQue: state.hintQue,
        setBSQ: () => {},
        pushBSQ: () => {},
        shiftBSQ: () => {},
        setBEQ: () => {},
        pushBEQ: () => {},
        shiftBEQ: () => {},
        setHQ: () => {},
        pushHQ: () => {},
        shiftHQ: () => {},
        readHQ: () => {},
      }),
    },
  ),
);

export { useNotifyStore };
export type { NotifyBaseOptions, HintItemOptions };
