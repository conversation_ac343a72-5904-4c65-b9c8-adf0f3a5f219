// Member types
import type {
  ColumnGroupType,
  ColumnsType,
  TablePaginationConfig,
} from 'antd/es/table';
import type {
  FilterValue,
  SorterResult,
  TableCurrentDataSource,
} from 'antd/es/table/interface';
import React from 'react';
import type { DayValue } from '@/utils/dateUtils';
import type { OrderTypeEnums } from '@/utils/enums';

export interface Member {
  id: string;
  name: string;
  passport: string;
  registrationDate: string;
  status: 'active' | 'inactive';
  email?: string;
  phone?: string;
  dateOfBirth?: string;
  nationality?: string;
  address?: string;
  totalOrders?: number;
  totalSpent?: number;
  lastVisit?: string;
  membershipTier?: 'bronze' | 'silver' | 'gold' | 'platinum';
  notes?: string;
}

export interface CreateMemberRequest {
  name: string;
  passport: string;
}

// Order types
export interface Order {
  id: string;
  memberId: string;
  memberName: string;
  amount: number;
  type: OrderTypeEnums;
  status:
    | 'pending'
    | 'completed'
    | 'confirmed'
    | 'rejected'
    | 'failed'
    | 'cancelled';
  paymentStatus: 'pending' | 'processing' | 'completed' | 'failed';
  date: string;
  qrCode?: string;
  description?: string;
  items?: OrderItem[];
  paymentMethod?: 'cash' | 'card' | 'digital_wallet' | 'crypto';
  transactionId?: string;
  notes?: string;
  createdBy?: string;
  updatedAt?: string;
  refundAmount?: number;
  refundReason?: string;
}

export interface OrderItem {
  id: string;
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  category?: string;
}

// Dashboard stats types
export interface DashboardStats {
  totalMembers: number;
  recentOrders: number;
  pendingTransactions: number;
  totalRevenue: number;
}

// API Response types
export interface ApiResponse<T> {
  data: T;
  success: boolean;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  pageSize: number;
}

// GalaxyTableProps2
export interface ColumnsWrxdieType {
  id: string;
  phoneNumber: string;
  userName: string;
  email: string;
  isNew: string;
  passportNumber: string;
  roles: string[];
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export type CustomColumnType<T> = ColumnGroupType<T> & {
  dataIndex: keyof T | (string & {});
  render?: (
    value: ColumnsWrxdieType,
    record: T,
    index: number,
  ) => React.ReactNode;
  sorter?: boolean | ((a: T, b: T) => number);
  filters?: { text: string; value: string }[];
  onFilter?: (value: string | number | boolean, record: T) => boolean;
};

export interface GalaxyTableWrxdie {
  current: number;
  pageSize: number;
  total: number;
  onChange: (page: number, pageSize: number) => void;
}

export interface GalaxyWrxdieTableProps<T> {
  data: T[];
  columns: ColumnsType<T>;
  loading?: boolean;
  customSort?: boolean;
  pagination?: GalaxyTableWrxdie;
  rowKey?: string | ((record: T) => string);
}

// Table props types
export interface GalaxyTableProps<T = any> {
  data: T[];
  columns: any[];
  loading?: boolean;
  customSort?: boolean;
  pagination?: {
    current: number;
    pageSize: number;
    total: number;

    onChange: (page: number, pageSize: number) => void;
  };
  onSortChange?: (sort: string[] | undefined) => void;
  onChange?: (
    pagination: TablePaginationConfig,
    filters: Record<string, FilterValue | null>,
    sorter: SorterResult<T> | SorterResult<T>[],
    extra: TableCurrentDataSource<T>,
  ) => void;
}

// Management types

// Employee types
export interface Employee {
  id: string;
  name: string;
  email: string;
  phone: string;
  role: 'admin' | 'manager' | 'cashier' | 'security' | 'maintenance';
  department:
    | 'management'
    | 'gaming'
    | 'security'
    | 'maintenance'
    | 'customer_service';
  status: 'active' | 'inactive' | 'suspended';
  hireDate: string;
  salary: number;
  permissions: string[];
  lastLogin?: string;
  performanceScore?: number;
  attendanceRate?: number;
  address?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  schedule?: EmployeeSchedule;
  attendanceRecords?: AttendanceRecord[];
  performanceMetrics?: PerformanceMetrics;
}

export interface EmployeeSchedule {
  monday: { start: string; end: string; off?: boolean };
  tuesday: { start: string; end: string; off?: boolean };
  wednesday: { start: string; end: string; off?: boolean };
  thursday: { start: string; end: string; off?: boolean };
  friday: { start: string; end: string; off?: boolean };
  saturday: { start: string; end: string; off?: boolean };
  sunday: { start: string; end: string; off?: boolean };
}

export interface AttendanceRecord {
  id: string;
  employeeId: string;
  date: string;
  clockIn?: string;
  clockOut?: string;
  breakStart?: string;
  breakEnd?: string;
  totalHours?: number;
  status: 'present' | 'absent' | 'late' | 'half_day' | 'sick' | 'vacation';
  notes?: string;
}

export interface PerformanceMetrics {
  monthlyRating: number;
  tasksCompleted: number;
  customerSatisfaction: number;
  punctuality: number;
  teamwork: number;
  lastReviewDate: string;
  goals: string[];
  achievements: string[];
}

export interface CreateEmployeeRequest {
  name: string;
  email: string;
  phone: string;
  role: Employee['role'];
  department: Employee['department'];
  salary: number;
  permissions: string[];
  address?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  schedule?: EmployeeSchedule;
}

export interface UpdateEmployeeRequest {
  name?: string;
  email?: string;
  phone?: string;
  role?: Employee['role'];
  department?: Employee['department'];
  salary?: number;
  permissions?: string[];
  address?: string;
  emergencyContact?: {
    name: string;
    phone: string;
    relationship: string;
  };
  schedule?: EmployeeSchedule;
}

// Role and Permission types
export interface Permission {
  id: string;
  name: string;
  description: string;
  category: PermissionCategory;
  actions: PermissionAction[];
}

export interface PermissionAction {
  id: string;
  name: string;
  description: string;
  type: 'view' | 'create' | 'edit' | 'delete' | 'manage';
}

export interface PermissionCategory {
  id: string;
  name: string;
  description: string;
  icon?: string;
}

export interface Role {
  id: string;
  name: string;
  description: string;
  status: 'active' | 'inactive';
  permissions: string[]; // Permission IDs
  userCount: number;
  isSystem: boolean; // System roles cannot be deleted
  createdAt: string;
  updatedAt: string;
  createdBy: string;
}

export interface UserRole {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  roleId: string;
  roleName: string;
  assignedAt: string;
  assignedBy: string;
}

export interface CreateRoleRequest {
  name: string;
  description: string;
  permissions: string[];
}

export interface UpdateRoleRequest {
  name?: string;
  description?: string;
  permissions?: string[];
  status?: Role['status'];
}

// Cash Register types
export interface CashRegister {
  id: string;
  name: string;
  location: string;
  status: 'open' | 'closed' | 'maintenance';
  currentBalance: number;
  openingBalance: number;
  employeeId: string;
  employeeName: string;
  openedAt?: string;
  closedAt?: string;
  shiftId?: string;
}

export interface Transaction {
  id: string;
  registerId: string;
  type: 'sale' | 'refund' | 'void' | 'cash_in' | 'cash_out';
  amount: number;
  description: string;
  employeeId: string;
  employeeName: string;
  timestamp: string;
  orderId?: string;
  receiptNumber?: string;
}

export interface Shift {
  id: string;
  registerId: string;
  employeeId: string;
  employeeName: string;
  startTime: string;
  endTime?: string;
  openingBalance: number;
  closingBalance?: number;
  totalSales: number;
  totalRefunds: number;
  status: 'active' | 'completed';
}

// Store types
export interface Store {
  id: string;
  name: string;
  location: string;
  address: string;
  phone: string;
  email: string;
  managerId: string;
  managerName: string;
  status: 'active' | 'inactive' | 'maintenance';
  openingHours: {
    monday: { open: string; close: string; closed?: boolean };
    tuesday: { open: string; close: string; closed?: boolean };
    wednesday: { open: string; close: string; closed?: boolean };
    thursday: { open: string; close: string; closed?: boolean };
    friday: { open: string; close: string; closed?: boolean };
    saturday: { open: string; close: string; closed?: boolean };
    sunday: { open: string; close: string; closed?: boolean };
  };
  totalRevenue: number;
  monthlyRevenue: number;
  employeeCount: number;
}

export interface Product {
  id: string;
  name: string;
  category: string;
  price: number;
  cost: number;
  stockLevel: number;
  minStockLevel: number;
  storeId: string;
  status: 'active' | 'inactive' | 'discontinued';
  lastRestocked?: string;
}

export interface CreateStoreRequest {
  name: string;
  location: string;
  address: string;
  phone: string;
  email: string;
  managerId: string;
  openingHours: Store['openingHours'];
}

// Management Dashboard Stats
export interface ManagementStats {
  totalEmployees: number;
  activeEmployees: number;
  totalStores: number;
  activeStores: number;
  totalRegisters: number;
  openRegisters: number;
  todayRevenue: number;
  monthlyRevenue: number;
  lowStockItems: number;
  pendingShifts: number;
}

// Accounting & Reporting Types
export interface FinancialStatement {
  id: string;
  type: 'income_statement' | 'balance_sheet' | 'cash_flow' | 'trial_balance';
  period: {
    startDate: string;
    endDate: string;
    type: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly';
  };
  data: FinancialData;
  generatedAt: string;
  generatedBy: string;
}

export interface FinancialData {
  revenue: {
    gaming: number;
    food: number;
    beverages: number;
    other: number;
    total: number;
  };
  expenses: {
    salaries: number;
    utilities: number;
    maintenance: number;
    supplies: number;
    marketing: number;
    other: number;
    total: number;
  };
  assets: {
    cash: number;
    inventory: number;
    equipment: number;
    other: number;
    total: number;
  };
  liabilities: {
    accountsPayable: number;
    loans: number;
    other: number;
    total: number;
  };
  equity: {
    capital: number;
    retainedEarnings: number;
    total: number;
  };
}
export interface AccountingStats {
  totalRevenue: number;
  totalExpenses: number;
  netIncome: number;
  totalAssets: number;
  totalLiabilities: number;
  equity: number;
  cashFlow: number;
  reconciliationStatus: {
    balanced: number;
    variance: number;
    pending: number;
  };
}
export interface TransactionSummary {
  id: string;
  period: {
    startDate: string;
    endDate: string;
  };
  summary: {
    totalTransactions: number;
    totalAmount: number;
    averageTransaction: number;
    byType: Record<
      Transaction['type'],
      {
        count: number;
        amount: number;
      }
    >;
    byPaymentMethod: Record<
      string,
      {
        count: number;
        amount: number;
      }
    >;
    byEmployee: Record<
      string,
      {
        employeeName: string;
        count: number;
        amount: number;
      }
    >;
    byRegister: Record<
      string,
      {
        registerName: string;
        count: number;
        amount: number;
      }
    >;
  };
  transactions: Record<Transaction['type'], TransactionDetail[]>;
  transactionsByPaymentMethod: Record<string, TransactionDetail[]>;
  transactionsByEmployee: Record<string, TransactionDetail[]>;
  transactionsByRegister: Record<string, TransactionDetail[]>;
  generatedAt: string;
}

export interface TransactionDetail {
  id: string;
  timestamp: string;
  amount: number;
  paymentMethod: string;
  employeeId: string;
  employeeName: string;
  registerId: string;
  registerName: string;
  description: string;
  reference: string;
  originalTransactionId?: string; // For refunds
  reason?: string; // For voids
}

export interface ReconciliationReport {
  id: string;
  registerId: string;
  registerName: string;
  shiftId?: string;
  period: {
    startDate: string;
    endDate: string;
  };
  openingBalance: number;
  closingBalance: number;
  expectedBalance: number;
  actualBalance: number;
  variance: number;
  transactions: {
    sales: number;
    refunds: number;
    cashIn: number;
    cashOut: number;
    voids: number;
  };
  discrepancies: {
    type: 'overage' | 'shortage';
    amount: number;
    reason?: string;
  }[];
  status: 'balanced' | 'variance' | 'pending';
  reconciledBy?: string;
  reconciledAt?: string;
  notes?: string;
}

export interface AccountingFilters {
  dateRange?: [string, string];
  registerId?: string;
  employeeId?: string;
  transactionType?: Transaction['type'];
  reportType?: FinancialStatement['type'];
  period?: FinancialStatement['period']['type'];
  reconciliationStatus?: ReconciliationReport['status'];
}

// Language types
export type Language = 'en' | 'fr';

// Navigation types
export interface MenuItem {
  key: string;
  label: string;
  icon?: React.ReactNode;
  path: string;
}

// Exchange Rate type
export interface ExchangeRate {
  cryptoSymbol: string;
  fiatCode: string;
  spotPrice: number;
  askPrice: number;
  bidPrice: number;
  updatedAt: string;
  autoUpdate: boolean;
  updateNote: string;
  autoUpdateSettings?: {
    frequency: 'hourly' | 'daily' | 'weekly';
    day?: DayValue;
    time?: string;
  };
}

export interface LoginProps {
  username: string;
  password: string;
}

export interface LoginRes {
  token: string;
  userId: string;
  email: string;
  phoneNumber: string;
  phoneRegionCode: string;
  roles: Array<string>;
}

export type YFRole = 'root' | 'admin' | 'staff' | 'user';

export interface RequestError {
  errorCode: number;
  errors: { [key: string]: Array<string> };
  status: number;
  title: string;
  type: string;
}

export type QueryKey = ReadonlyArray<unknown>;
export type ValueOf<T> = T[keyof T];
export type ReactSet<T> = React.Dispatch<React.SetStateAction<T>>;

// API Filtering and Sorting Types
export type FilterOperator =
// String operators
  | 'eq' | 'ne' | 'c' | 'sw' | 'ew'
  // Number operators
  | 'gt' | 'gte' | 'lt' | 'lte';

export type SortDirection = 'asc' | 'desc';

export interface ApiFilter {
  key: string;
  operator: FilterOperator;
  value: string | number | boolean;
}

export interface ApiSort {
  key: string;
  direction: SortDirection;
}

export interface ApiFiltersParams {
  filters?: ApiFilter[];
  sorts?: ApiSort[];
  page?: number;
  pageSize?: number;
}

export interface FilterConfig {
  key: string;
  type: 'string' | 'number' | 'date' | 'boolean' | 'uuid' | 'enum';
  operators?: FilterOperator[];
}

export interface SortConfig {
  key: string;
  label: string;
}

export interface UseApiFiltersConfig {
  defaultFilters?: ApiFilter[];
  defaultSorts?: ApiSort[];
  defaultPage?: number;
  defaultPageSize?: number;
  filterConfigs?: FilterConfig[];
  sortConfigs?: SortConfig[];
}
