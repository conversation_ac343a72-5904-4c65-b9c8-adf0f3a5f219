// Application routes
export const ROUTES = {
  ROOT: '/',
  LOGIN: '/login',
  DASHBOARD: '/dashboard',
  MEMBERS: '/members',
  ORDERS: '/orders',
  MANAGEMENT: '/management',
  ACCOUNTING: '/accounting',
  ROLE_PERMISSION: '/role-permission',
  SETTINGS: '/settings',
  PROFILE: '/profile',
} as const;

// Route metadata
export const ROUTE_METADATA = {
  [ROUTES.LOGIN]: {
    key: 'login',
    titleKey: 'login.title',
  },
  [ROUTES.DASHBOARD]: {
    key: 'dashboard',
    titleKey: 'dashboard.title',
    icon: 'DashboardOutlined',
  },
  [ROUTES.MEMBERS]: {
    key: 'members',
    titleKey: 'members.title',
    icon: 'UserOutlined',
  },
  [ROUTES.ORDERS]: {
    key: 'orders',
    titleKey: 'orders.title',
    icon: 'ShoppingCartOutlined',
  },
  [ROUTES.MANAGEMENT]: {
    key: 'management',
    titleKey: 'management.title',
    icon: 'SettingOutlined',
  },
  [ROUTES.ACCOUNTING]: {
    key: 'accounting',
    titleKey: 'accounting.title',
    icon: 'CalculatorOutlined',
  },
  [ROUTES.ROLE_PERMISSION]: {
    key: 'role-permission',
    titleKey: 'rolePermission.title',
    icon: 'SafetyOutlined',
  },
  [ROUTES.SETTINGS]: {
    key: 'settings',
    titleKey: 'settings.title',
    icon: 'SettingOutlined',
  },
  [ROUTES.PROFILE]: {
    key: 'profile',
    titleKey: 'profile.title',
    icon: 'ProfileOutlined',
  },
} as const;

export type RouteKey = keyof typeof ROUTE_METADATA;
